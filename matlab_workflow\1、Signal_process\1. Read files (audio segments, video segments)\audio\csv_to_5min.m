%% CSV文件合并与5分钟数据分割工具
% =========================================================================
% 功能：将多个CSV文件按顺序合并，重新分割成771,000行的标准文件
%
% 输入：包含多个CSV文件的文件夹（文件名需包含数字用于排序）
% 输出：在同级目录"2、Processed data"文件夹中生成dataN_5min.csv文件
%
% 主要步骤：
%   1. 选择包含CSV文件的文件夹
%   2. 按文件名中的数字排序
%   3. 逐个读取CSV文件并合并数据
%   4. 每771,000行保存为一个新文件
%
% 用途：音频信号处理数据预处理，生成标准5分钟数据段
% =========================================================================

clear all;
clc;
close all;

% 选择文件夹
folderPath = uigetdir('', '选择文件夹'); % 弹出文件夹选择框
if folderPath == 0
    disp('用户取消了文件夹选择');
    return;
end

% 获取文件夹中的所有csv文件
csvFiles = dir(fullfile(folderPath, '*.csv'));

% 如果没有找到csv文件，退出程序
if isempty(csvFiles)
    disp('文件夹中没有找到csv文件');
    return;
end

% 提取文件名中的数字并排序
fileNumbers = zeros(length(csvFiles), 1);
for i = 1:length(csvFiles)
    % 使用正则表达式提取文件名中的数字
    fileName = csvFiles(i).name;
    numbers = regexp(fileName, '\d+', 'match');
    if ~isempty(numbers)
        fileNumbers(i) = str2double(numbers{1});
    end
end

% 根据提取的数字对文件进行排序
[~, sortIdx] = sort(fileNumbers);
csvFiles = csvFiles(sortIdx);

% 设置每个新文件需要的行数
numDataPerFile = 771000; % 每个新文件的目标数据量（771000行）

% 创建输出文件夹，如果不存在的话
outputFolder = fullfile(pwd, '2、Processed data');
if ~exist(outputFolder, 'dir')
    mkdir(outputFolder);
end

% 初始化数据容器
combinedData = [];

% 文件计数器，用于生成新文件名
fileCounter = 1;

% 遍历排序后的csv文件
for i = 1:length(csvFiles)
    % 获取当前csv文件的完整路径
    currentFileName = fullfile(folderPath, csvFiles(i).name);
    
    % 读取csv文件
    data = readtable(currentFileName);
    
    % 删除第一行
    data(1, :) = [];
    
    % 将当前文件的数据与已经合并的数据合并
    combinedData = [combinedData; data];
    
    % 如果合并后的数据行数已经达到目标值
    while height(combinedData) >= numDataPerFile
        % 截取前 numDataPerFile 行数据
        dataToSave = combinedData(1:numDataPerFile, :);
        
        % 生成新文件名：data1_5min.csv, data2_5min.csv, ...
        newFileName = sprintf('data%d_5min.csv', fileCounter);
        newFilePath = fullfile(outputFolder, newFileName);
        
        % 保存处理后的数据为新的CSV文件
        writetable(dataToSave, newFilePath);
        
        % 输出提示信息
        disp(['已保存文件: ', newFileName]);
        
        % 删除已保存的部分数据，继续合并剩余的数据
        combinedData(1:numDataPerFile, :) = [];
        
        % 更新文件计数器
        fileCounter = fileCounter + 1;
    end
end

% 如果最后剩余的数据不足 771000 行，但有数据
if height(combinedData) > 0
    % 生成新文件名：dataN_5min.csv
    newFileName = sprintf('data%d_5min.csv', fileCounter);
    newFilePath = fullfile(outputFolder, newFileName);
    
    % 保存剩余的数据
    writetable(combinedData, newFilePath);
    disp(['已保存文件: ', newFileName]);
end

disp('所有文件处理完成');
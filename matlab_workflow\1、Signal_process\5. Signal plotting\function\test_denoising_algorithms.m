% 文件：test_denoising_algorithms.m
% 描述：测试不同的降噪算法并比较信噪比 (SNR)

clear all
clc
close all

%% CSV文件读取&预处理
%*****************************************************************************************
%
%   1、读取选择好的CSV文件
%   2、绘制原始波形
%
%*****************************************************************************************

% 1、读取选择好的CSV文件
M_Folders = "Body_Sound";
[file, path] = uigetfile(fullfile(M_Folders, '*.csv'), 'Select the CSV file');
if isequal(file, 0)
    disp('用户取消了文件选择');
    return;
end
filename = fullfile(path, file);
data = readmatrix(filename);
mic1 = data(:, 2); % 假设信号在第二列
% mic2 = data(:, 3); % 假设信号在第三列
fs = 2570; % 采样率
t1 = (0:length(mic1) - 1) / fs; % 时间刻度

% 2、绘制原始波形
% figure('Position', [300, 300, 1500, 900]);
% plot_waveform(t1, mic1, 2, 1, 1, 'Raw data of Mic (Body)');
% plot_spectrogram(mic1, fs, 2, 1, 2, 'Spectrogram of Raw data of Mic (Body)');

%% 带通滤波
%*****************************************************************************************
%
%   1、带通处理
%   2、显示滤波后的波形
%   3、计算带通滤波后的SNR
%
%*****************************************************************************************
%1、带通滤波
low_freq = 100; % 带通滤波的低频边界
high_freq = 800; % 带通滤波的高频边界
mic1_filtered = bandpass(mic1, [low_freq high_freq], fs);

%2、显示滤波后的波形
figure('Position', [300, 300, 1500, 900]);
plot_waveform(t1, mic1_filtered, 2, 1, 1, 'After band-pass filter (100-800Hz)');
plot_spectrogram(mic1_filtered, fs, 2, 1, 2, 'Spectrogram after band-pass filter (100-800Hz)');

%3、计算带通滤波后的SNR
snr_bandpass = SNR_singlech(mic1_filtered, mic1);
fprintf('带通滤波后的 SNR = %.4f dB\n', snr_bandpass);


%************************************************************************************%
% 文件：test_denoising_algorithms.m
% 描述：测试不同的降噪算法并比较信噪比 (SNR)




% 参数设定
wlen = 200;               % 帧长
inc = 80;                 % 帧移
NIS = 10;                 % 前导无语音帧数
IS = 0.15;                % 静默段比例
t1 = (0:length(mic1_filtered)-1)/fs; % 时间轴
N=length(mic1_filtered);                            % 信号长度
% 1. 传统谱减法降噪
a = 4;                    % 谱减法参数
b = 0.001;                % 谱减法参数
output_trad = simplesubspec(mic1_filtered, wlen, inc, NIS, a, b);
snr_trad = SNR_singlech(output_trad, mic1_filtered);

% 2. Boll谱减法降噪
output_boll = SSBoll79(mic1_filtered, fs, IS);
ol_boll = length(output_boll);
if ol_boll < length(mic1_filtered)
    output_boll = [output_boll; zeros(length(mic1_filtered)-ol_boll, 1)];
end
snr_boll = SNR_singlech(output_boll, mic1_filtered);

% 3. 使用SSBoll79m_2谱减法
[output_ssbollm2, voiceseg, vosl, SF, Ef] = SSBoll79m_2(mic1_filtered, fs, IS, 0.12);
ol_ssbollm2 = length(output_ssbollm2);
if ol_ssbollm2 < N
    output_ssbollm2 = [output_ssbollm2; zeros(N-ol_ssbollm2, 1)];
end
snr_ssbollm2 = SNR_singlech(output_ssbollm2, mic1_filtered);

% 4. 多窗谱估计谱减法
alpha = 2.8;              % 过减因子
beta = 0.001;             % 增益补偿因子
c = 1;                    % 控制增益矩阵开方
output_mtmpsd = Mtmpsd_ssb(mic1_filtered, wlen, inc, NIS, alpha, beta, c);
snr_mtmpsd = SNR_singlech(output_mtmpsd, mic1_filtered);

% 5. 维纳滤波降噪
output_wiener = WienerScalart96m_2(mic1_filtered, fs, IS, 0.12);
ol_wiener = length(output_wiener);
if ol_wiener < length(mic1_filtered)
    output_wiener = [output_wiener; zeros(length(mic1_filtered)-ol_wiener, 1)];
end
snr_wiener = SNR_singlech(output_wiener, mic1_filtered);

% 显示不同算法的信噪比结果
fprintf('传统谱减法 SNR = %.4f dB\n', snr_trad);
fprintf('Boll谱减法 SNR = %.4f dB\n', snr_boll);
fprintf('SSBoll79m_2谱减法 SNR = %.4f dB\n', snr_ssbollm2);
fprintf('多窗谱估计谱减法 SNR = %.4f dB\n', snr_mtmpsd);
fprintf('维纳滤波 SNR = %.4f dB\n', snr_wiener);

% 可视化每种算法的输出
figure('Position', [300, 300, 1800, 1200]);

% 1. 传统谱减法输出
subplot(5, 2, 1);
plot_waveform(t1, output_trad, 2, 1, 1, 'Traditional Spectral Subtraction Output');
subplot(5, 2, 2);
plot_spectrogram(output_trad, fs, 2, 1, 2, 'Spectrogram of Traditional Spectral Subtraction Output');

% 2. Boll谱减法输出
subplot(5, 2, 3);
plot_waveform(t1, output_boll, 2, 1, 1, 'Boll Spectral Subtraction Output');
subplot(5, 2, 4);
plot_spectrogram(output_boll, fs, 2, 1, 2, 'Spectrogram of Boll Spectral Subtraction Output');

% 3. SSBoll79m_2谱减法输出
subplot(5, 2, 5);
plot_waveform(t1, output_ssbollm2, 2, 1, 1, 'SSBoll79m_2 Spectral Subtraction Output');
subplot(5, 2, 6);
plot_spectrogram(output_ssbollm2, fs, 2, 1, 2, 'Spectrogram of SSBoll79m_2 Spectral Subtraction Output');

% 4. 多窗谱估计谱减法输出
subplot(5, 2, 7);
plot_waveform(t1, output_mtmpsd, 2, 1, 1, 'Multi-window PSD Spectral Subtraction Output');
subplot(5, 2, 8);
plot_spectrogram(output_mtmpsd, fs, 2, 1, 2, 'Spectrogram of Multi-window PSD Spectral Subtraction Output');

% 5. 维纳滤波输出
subplot(5, 2, 9);
plot_waveform(t1, output_wiener, 2, 1, 1, 'Wiener Filter Output');
subplot(5, 2, 10);
plot_spectrogram(output_wiener, fs, 2, 1, 2, 'Spectrogram of Wiener Filter Output');

% 比较 SNR
% algorithm_names = {'传统谱减法', 'Boll谱减法', 'SSBoll79m_2谱减法', '多窗谱估计谱减法', '维纳滤波'};
% snr_values = [snr_trad, snr_boll, snr_ssbollm2, snr_mtmpsd, snr_wiener];
% 
% fig_position = [100, 100, 1000, 600];  % 图像位置 (x, y) 和大小 (宽度, 高度)
% title_text = '不同降噪算法的信噪比比较';
% plot_bar_snr(snr_values, algorithm_names, title_text, fig_position);

algorithm_names = {'Traditional Spectral Subtraction', 'Boll Spectral Subtraction', 'SSBoll79m_2 Spectral Subtraction', 'Multi-window PSD Spectral Subtraction', 'Wiener Filter'};
snr_values = [snr_trad, snr_boll, snr_ssbollm2, snr_mtmpsd, snr_wiener];

fig_position = [100, 100, 1000, 600];  % 图像位置 (x, y) 和大小 (宽度, 高度)
title_text = 'SNR Comparison of Different Noise Reduction Algorithms';
plot_bar_snr(snr_values, algorithm_names, title_text, fig_position);

function plot_spectrogram(data, fs, position, rows, cols, index, title_text)
    w = 0.05;
    win = round(fs * w);
    ov = round(fs * w * 0.9);
    nfft = round(fs * 0.5);
    [S, F, T, P] = spectrogram(data, win, ov, nfft, fs);
    
    figure('Position', position); % 设置图框位置和大小，[left, bottom, width, height]
    subplot(rows, cols, index);
    surf(T, F, 10 * log10(P), 'edgecolor', 'none');
    axis tight;
    view(0, 90);
    caxis([-90 -50]);
    colorbar;
    colormap(inferno);
    ax = gca;
    ax.FontSize = 16; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = "bold";
    ax.XLim = [0 300];
    xticks([0 50 100 150 200 250 300]); % 自定义纵坐标刻度
    xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Freq.(Hz)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
end
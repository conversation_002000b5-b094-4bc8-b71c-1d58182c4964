# {{model_name}} - Simulink Model Specification

[[LLM: This template guides the creation of comprehensive Simulink model specifications. Focus on system requirements, model architecture, and validation criteria. Present numbered options for different modeling approaches.]]

## Model Overview

[[LLM: Begin by understanding the system to be modeled and the simulation objectives.]]

**Model Name:** {{model_name}}
**System Type:** {{system_type}}
**Model Purpose:** {{model_purpose}}
**Designer:** {{designer}}
**Design Date:** {{design_date}}

### System Description

[[LLM: Gather detailed information about the physical system or process being modeled.]]

{{system_description}}

### Modeling Objectives

[[LLM: Present numbered options for different modeling objectives:
1. System analysis and understanding
2. Control system design and validation
3. Performance optimization
4. Real-time simulation and testing
5. Hardware-in-the-loop simulation
6. Code generation and deployment]]

**Primary Objectives:**
{{primary_objectives}}

**Success Criteria:**
{{success_criteria}}

## System Requirements

[[LLM: Define the system requirements and constraints that the model must satisfy.]]

### Functional Requirements

**System Inputs:**
{{system_inputs}}

**System Outputs:**
{{system_outputs}}

**System Behavior:**
{{system_behavior}}

**Operating Conditions:**
{{operating_conditions}}

### Performance Requirements

**Real-time Constraints:**
{{realtime_constraints}}

**Accuracy Requirements:**
{{accuracy_requirements}}

**Computational Performance:**
{{computational_performance}}

**Memory Constraints:**
{{memory_constraints}}

## Model Architecture

[[LLM: Design the overall model architecture and structure. Consider different modeling approaches based on system type.]]

### Top-Level Architecture

[[LLM: Describe the high-level model structure and main subsystems.]]

**Model Hierarchy:**
{{model_hierarchy}}

**Main Subsystems:**
{{main_subsystems}}

**Signal Flow:**
{{signal_flow}}

**Interface Design:**
{{interface_design}}

### Subsystem Specifications

[[LLM: For each major subsystem, gather detailed specifications.]]

<<REPEAT section="subsystem" count="{{subsystem_count}}">>
**Subsystem {{subsystem_number}}:** {{subsystem_name}}
- **Purpose:** {{subsystem_purpose}}
- **Inputs:** {{subsystem_inputs}}
- **Outputs:** {{subsystem_outputs}}
- **Dynamics:** {{subsystem_dynamics}}
- **Parameters:** {{subsystem_parameters}}
<</REPEAT>>

## Mathematical Model

[[LLM: Define the mathematical foundation of the model.]]

### System Equations

**Governing Equations:**
{{governing_equations}}

**State Variables:**
{{state_variables}}

**Parameters:**
{{model_parameters}}

**Assumptions:**
{{model_assumptions}}

### Model Fidelity

[[LLM: Present options for model fidelity levels:
1. High-fidelity detailed model
2. Medium-fidelity functional model
3. Low-fidelity conceptual model
4. Multi-fidelity adaptive model]]

**Selected Fidelity Level:** {{fidelity_level}}
**Justification:** {{fidelity_justification}}

**Simplifications:**
{{model_simplifications}}

**Limitations:**
{{model_limitations}}

## Implementation Specifications

[[LLM: Define specific implementation requirements for Simulink.]]

### Block Selection and Configuration

**Primary Blocks:**
{{primary_blocks}}

**Custom Blocks:**
{{custom_blocks}}

**Masked Subsystems:**
{{masked_subsystems}}

**Library Dependencies:**
{{library_dependencies}}

### Signal and Data Types

**Signal Specifications:**
{{signal_specifications}}

**Data Types:**
{{data_types}}

**Sample Times:**
{{sample_times}}

**Bus Objects:**
{{bus_objects}}

### Parameter Management

**Parameter Organization:**
{{parameter_organization}}

**Parameter Sources:**
{{parameter_sources}}

**Tunable Parameters:**
{{tunable_parameters}}

**Parameter Validation:**
{{parameter_validation}}

## Simulation Configuration

[[LLM: Define simulation setup and configuration requirements.]]

### Solver Configuration

**Solver Type:** {{solver_type}}
**Step Size:** {{step_size}}
**Simulation Time:** {{simulation_time}}
**Tolerance Settings:** {{tolerance_settings}}

### Test Scenarios

[[LLM: Define different test scenarios and simulation conditions.]]

<<REPEAT section="scenario" count="{{scenario_count}}">>
**Scenario {{scenario_number}}:** {{scenario_name}}
- **Description:** {{scenario_description}}
- **Initial Conditions:** {{initial_conditions}}
- **Input Signals:** {{input_signals}}
- **Expected Behavior:** {{expected_behavior}}
- **Success Criteria:** {{scenario_success_criteria}}
<</REPEAT>>

### Data Logging and Visualization

**Logged Signals:**
{{logged_signals}}

**Visualization Requirements:**
{{visualization_requirements}}

**Output Data Format:**
{{output_data_format}}

**Post-processing Requirements:**
{{postprocessing_requirements}}

## Validation and Verification

[[LLM: Define comprehensive validation and verification strategy.]]

### Model Validation

**Validation Approach:**
{{validation_approach}}

**Reference Data:**
{{reference_data}}

**Validation Metrics:**
{{validation_metrics}}

**Acceptance Criteria:**
{{acceptance_criteria}}

### Verification Strategy

**Unit Testing:**
{{unit_testing}}

**Integration Testing:**
{{integration_testing}}

**System Testing:**
{{system_testing}}

**Performance Testing:**
{{performance_testing}}

## Documentation Requirements

[[LLM: Define documentation standards and requirements.]]

### Model Documentation

**Model Description:**
{{model_documentation}}

**Block Documentation:**
{{block_documentation}}

**Parameter Documentation:**
{{parameter_documentation}}

**Usage Instructions:**
{{usage_instructions}}

### User Documentation

**User Guide:**
{{user_guide}}

**Installation Instructions:**
{{installation_instructions}}

**Troubleshooting Guide:**
{{troubleshooting_guide}}

**Examples and Tutorials:**
{{examples_tutorials}}

## Quality Assurance

[[LLM: Define quality standards and assessment criteria.]]

### Quality Standards

**Model Quality Criteria:**
{{model_quality_criteria}}

**Documentation Standards:**
{{documentation_standards}}

**Performance Standards:**
{{performance_standards}}

**Maintainability Standards:**
{{maintainability_standards}}

### Review and Approval

**Design Review:**
{{design_review}}

**Implementation Review:**
{{implementation_review}}

**Validation Review:**
{{validation_review}}

**Final Approval:**
{{final_approval}}

## Deployment and Maintenance

[[LLM: Define deployment strategy and maintenance requirements.]]

### Deployment Strategy

^^CONDITION: deployment_type == "code_generation"^^
**Code Generation Requirements:**
{{code_generation_requirements}}

**Target Platform:**
{{target_platform}}

**Real-time Constraints:**
{{realtime_deployment_constraints}}
^^/CONDITION: deployment_type^^

^^CONDITION: deployment_type == "simulation_only"^^
**Simulation Environment:**
{{simulation_environment}}

**Distribution Requirements:**
{{distribution_requirements}}

**User Training:**
{{user_training}}
^^/CONDITION: deployment_type^^

### Maintenance Plan

**Update Procedures:**
{{update_procedures}}

**Version Control:**
{{version_control}}

**Support Documentation:**
{{support_documentation}}

**Maintenance Schedule:**
{{maintenance_schedule}}

## Approval and Sign-off

[[LLM: After completing all sections, present the specification for review and approval.]]

**Specification Prepared By:** {{prepared_by}}
**Date:** {{preparation_date}}
**Technical Review:** {{technical_review}}
**Stakeholder Approval:** {{stakeholder_approval}}
**Implementation Authorization:** {{implementation_authorization}}

---

[[LLM: After completing the model specification, suggest next steps:
1. Review and validate system requirements
2. Approve model architecture and approach
3. Begin detailed Simulink model implementation
4. Set up validation and testing framework
5. Plan deployment and maintenance procedures]]

function featTable = featuresTable(features, labels)
% FEATURESTABLE 将MFCC特征与标签对应起来，并转换为一个表
%
% 输入参数:
%   features - MFCC特征的单元数组
%   labels - 对应的标签向量
%
% 输出参数:
%   featTable - 包含MFCC特征和标签的表格


% 将单元数组转换为矩阵
    numSamples = size(features, 1);
    numFeatures = numel(features{1});
    featuresMatrix = zeros(numSamples, numFeatures);
    
    for i = 1:numSamples
        featuresMatrix(i, :) = reshape(features{i}, 1, []);
    end
    
    % 将特征矩阵转换为表格
    featlabels = arrayfun(@(x) ['Feature' num2str(x)], 1:numFeatures, 'UniformOutput', false);
    featT = array2table(featuresMatrix, 'VariableNames', featlabels);
    
    % 创建标签表格
    labelsT = table(categorical(labels), 'VariableNames', {'Label'});
    
    % 合并特征表和标签表
    featTable = [featT, labelsT];
end























% % 获取样本数量和特征数量
% numSamples = length(features);
% numFeatures = size(features{1}, 2);
% 
% % 初始化特征矩阵
% featMatrix = zeros(numSamples, numFeatures);
% 
% % 将单元数组中的特征转换为矩阵
% for i = 1:numSamples
%     featMatrix(i, :) = mean(features{i}, 1);  % 计算每个样本的MFCC均值
% end
% 
% % 创建包含特征的表
% featT = array2table(featMatrix);
% 
% % 将标签转换为分类变量，并创建一个表
% labelsT = table(categorical(labels), 'VariableNames', {'Label'});
% 
% % 将特征表和标签表水平拼接，形成最终的特征表
% featTable = [featT, labelsT];
% end

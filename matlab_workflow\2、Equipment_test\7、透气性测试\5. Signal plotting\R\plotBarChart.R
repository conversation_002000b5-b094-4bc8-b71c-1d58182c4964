# ==========================================
# 文件名：plotBarChart.R
# 主要功能：绘制渐变色柱状图（MVTR数据可视化）
# 依赖文件：无
# 主要函数：
#   - plotBarChart(): 绘制材料-数值渐变色柱状图
# 输入参数：材料名称向量，数值向量，样式参数
# 输出结果：返回ggplot对象供用户手动保存
# 创建日期：2025-08-01
# ==========================================

# 加载必要的包
library(ggplot2)
library(dplyr)
library(readr)
library(scales)

#' 绘制渐变色柱状图
#' 专为材料数据可视化设计的柱状图绘制函数
#'
#' @param materials 材料名称向量
#' @param values 对应的数值向量
#' @param gradient_low 渐变起始色（浅色，对应低数值）
#' @param gradient_high 渐变结束色（深色，对应高数值）
#' @param sort_by_value 是否按数值排序
#' @param y_limits Y轴范围
#' @param value_format 数值标签格式
#' @param x_label X轴标题
#' @param y_label Y轴标题
#' @return ggplot对象
plotBarChart <- function(materials,
                        values,
                        gradient_low = "#E3F2FD",
                        gradient_high = "#1976D2",
                        sort_by_value = TRUE,
                        y_limits = c(0, 1.2),
                        value_format = "%.2f",
                        x_label = "Substate Type",
                        y_label = "MVTR (g/(m²·24h))") {

  # 创建数据框
  plot_data <- data.frame(
    Material = as.character(materials),
    Value = as.numeric(values)
  )

  # 创建渐变主题图表
  if (sort_by_value) {
    p <- ggplot(plot_data, aes(x = reorder(Material, Value),
                               y = Value,
                               fill = Value))
  } else {
    p <- ggplot(plot_data, aes(x = Material,
                               y = Value,
                               fill = Value))
  }

  p <- p +
    # 柱状图样式设置
    geom_col(alpha = 0.9, width = 0.7) +

    # 颜色设置：渐变色配置
    scale_fill_gradient(low = gradient_low,
                        high = gradient_high,
                        guide = "none") +

    # Y轴范围设置
    scale_y_continuous(limits = y_limits,
                       expand = expansion(mult = c(0, 0.05))) +

    # 数值标签设置：柱状图顶部的数字
    geom_text(aes(label = sprintf(value_format, Value)),
              vjust = -0.5,
              size = 4,
              fontface = "plain",
              color = "black",
              family = "Times New Roman") +

    # 坐标轴标签设置
    labs(x = x_label, y = y_label)

  # 应用主题样式，与 plot_mvtr_bar.R 保持一致
  p <- p +
    theme_bw() +
    theme(
      # 背景颜色设置
      plot.background = element_rect(fill = "white", color = NA),
      panel.background = element_rect(fill = "white", color = NA),

      # 字体设置：全局字体配置
      text = element_text(family = "Times New Roman", color = "black"),

      # 坐标轴文字设置
      axis.text.x = element_text(angle = 0, hjust = 0.5, size = 11, color = "black"),
      axis.text.y = element_text(size = 10, color = "black"),

      # 坐标轴标题设置
      axis.title = element_text(size = 12, face = "bold", color = "black"),

      # 网格线设置
      panel.grid = element_blank(),

      # 完整边框设置：使用面板边框（四个边框等粗）
      axis.line = element_blank(),
      panel.border = element_rect(color = "black", fill = NA, linewidth = 0.6),

      # 坐标轴刻度线设置
      axis.ticks = element_line(color = "black", linewidth = 0.6),
      axis.ticks.length = unit(-0.2, "cm"),  # 负值表示朝内

      # 布局设置：图表边距
      plot.margin = margin(10, 10, 0, 10)
    )

  return(p)
}

# MVTR 项目专用函数，保留原函数名
plot_mvtr_bar_advanced <- function(csv_file_path) {

  # 读取数据
  data <- read_csv(csv_file_path, locale = locale(encoding = "UTF-8"))
  materials <- data[[1]]
  values <- data[[ncol(data)]]

  # 调用重构后的 plotBarChart
  p <- plotBarChart(materials = materials,
                    values = values)

  # 显示图表
  print(p)

  # 返回图表对象（可用于进一步修改或保存）
  return(p)
}

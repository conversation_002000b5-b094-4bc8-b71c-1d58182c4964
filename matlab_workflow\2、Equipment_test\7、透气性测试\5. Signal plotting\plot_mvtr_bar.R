

library(ggplot2)
library(dplyr)
library(readr)
library(scales)
library(RColorBrewer)

# 创建渐变主题柱状图
plot_mvtr_bar_advanced <- function(csv_file_path) {
  
  # 读取数据
  data <- read_csv(csv_file_path, locale = locale(encoding = "UTF-8"))
  materials <- data[[1]]
  values <- data[[ncol(data)]]
  
  plot_data <- data.frame(
    Material = as.character(materials),
    Value = as.numeric(values)
  )
  
  # 创建渐变主题图表
  p <- ggplot(plot_data, aes(y = Value, fill = Value)) +
    # 柱状图样式设置
    geom_col(alpha = 0.9,    # 透明度：0-1，0完全透明，1完全不透明
             width = 0.7) +  # 柱状图宽度：0-1，数值越大柱子越宽
    # 颜色设置：渐变色配置
    scale_fill_gradient(low = "#E3F2FD",    # 渐变起始色（浅色，对应低数值）
                        high = "#1976D2",    # 渐变结束色（深色，对应高数值）
                        guide = "none") +    # 不显示颜色图例
    # Y轴范围设置：让Y轴0值与X轴重合
    scale_y_continuous(limits = c(0, 1.2),              # Y轴范围：c(最小值, 最大值)，可改为c(0, 50)、c(10, 80)等
                       expand = expansion(mult = c(0, 0.05))) +  # 下方无扩展，上方5%扩展
    # 基础主题设置：使用theme_bw()显示完整边框
    theme_bw() +
    theme(
      # 背景颜色设置
      plot.background = element_rect(fill = "white", color = NA),    # 整体背景色
      panel.background = element_rect(fill = "white", color = NA),   # 绘图区背景色
      
      # 字体设置：全局字体配置
      text = element_text(family = "Times New Roman",  # 字体类型：可改为"Arial", "Helvetica", "SimSun"等
                          color = "black"),             # 全局文本颜色：可改为其他颜色如"#333333"
      
      # 坐标轴文字设置
      axis.text.x = element_text(angle = 0,        # X轴文字角度：0水平，45倾斜，90垂直
                                 hjust = 0.5,      # 水平对齐：0左对齐，0.5居中，1右对齐
                                 size = 11,        # X轴文字大小：可调整为8-16
                                 color = "black"), # X轴文字颜色
      axis.text.y = element_text(size = 10,        # Y轴文字大小：可调整为8-16
                                 color = "black"), # Y轴文字颜色
      
      # 坐标轴标题设置
      axis.title = element_text(size = 12,         # 轴标题字号：可调整为10-18
                                face = "bold",     # 字体粗细：normal普通，bold粗体
                                color = "black"),  # 轴标题颜色
      
      # 网格线设置
      panel.grid = element_blank(),  # 移除所有网格线，如需网格线可设为element_line()
      
      # 完整边框设置：使用面板边框（四个边框等粗）
      axis.line = element_blank(),              # 移除轴线，避免与面板边框重复

      # 面板边框设置（theme_bw()默认提供，这里自定义调整）
      panel.border = element_rect(color = "black",  # 边框颜色
                                  fill = NA,         # 边框内部不填充
                                  linewidth = 0.6),  # 边框粗细，统一设置
      
      # 坐标轴刻度线设置
      axis.ticks = element_line(color = "black",    # 刻度线颜色
                                linewidth = 0.6),   # 刻度线粗细：与边框保持一致
      axis.ticks.length = unit(-0.2, "cm"),         # 刻度线长度：正值朝外，负值朝内
      
      # 布局设置：图表边距
      plot.margin = margin(10, 10, 0, 10)  # 边距：上右下左，可调整为5-20
    )
  
  # 添加通用元素
  p <- p +
    # 坐标轴数据映射：按数值大小排序
    aes(x = reorder(Material, Value)) +  # 可改为reorder(Material, -Value)实现降序排列
    
    # 坐标轴标签设置
    labs(x = "Substate Type",        # X轴标签文字：可自定义为任意文字
         y = "MVTR (g/(m²·24h))") +  # Y轴标签文字：可自定义单位和格式
    
    # 数值标签设置：柱状图顶部的数字
    geom_text(aes(label = sprintf("%.2f", Value)),  # 数值格式：%.2f保留2位小数，%.1f保留1位
              vjust = -0.5,          # 垂直位置：-0.5在柱子上方，0.5在柱子下方，0在柱子中间
              size = 4,              # 数值标签字号：可调整为2-6
              fontface = "plain",      # 字体粗细：plain普通，bold粗体，italic斜体
              color = "black",       # 数值标签颜色：可改为其他颜色
              family = "Times New Roman")  # 数值标签字体：与全局字体保持一致
  
  # 显示图表
  print(p)
  
  # 返回图表对象（可用于进一步修改或保存）
  return(p)
}

# ========== 使用示例 ==========
# 基本使用：读取CSV文件并绘制图表
plot_mvtr_bar_advanced("results.csv")

# 常见自定义修改示例：
# 1. 修改颜色：将渐变色改为红色系
#    scale_fill_gradient(low = "#FFEBEE", high = "#D32F2F", guide = "none")
# 2. 修改字体：将Times New Roman改为Arial
#    text = element_text(family = "Arial", color = "black")
# 3. 调整柱子宽度：将0.7改为0.5（更窄）或0.9（更宽）
#    geom_col(alpha = 0.9, width = 0.5)
# 4. 修改边框粗细：将0.4改为1.0（更粗）
#    panel.border = element_rect(color = "black", fill = NA, linewidth = 1.0)
#    axis.line = element_line(color = "black", linewidth = 1.0)
#    axis.ticks = element_line(color = "black", linewidth = 1.0)
# 5. 调整数值标签位置：将-0.5改为-0.3（更靠近柱子）
#    vjust = -0.3
# 6. 修改Y轴范围：将c(0, 100)改为其他范围
#    limits = c(0, 50)：设置Y轴范围为0到50
#    limits = c(10, 80)：设置Y轴范围为10到80
#    limits = c(0, NA)：设置最小值为0，最大值自动调整
#    limits = c(NA, 100)：设置最大值为100，最小值自动调整
# 7. 完整边框说明：
#    - theme_bw() 提供完整的四边框
#    - panel.border 控制外边框
#    - axis.line 控制坐标轴线（在theme_bw()中通常被panel.border覆盖）
#    - 所有linewidth参数保持一致可确保边框等粗







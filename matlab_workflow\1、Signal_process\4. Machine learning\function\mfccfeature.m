function features = mfccfeature(rawDataset, fs, win, overlapLength)
    % MFCCFEATURE 提取MFCC特征
    %   features = MFCCFEATURE(rawDataset, fs, win, overlapLength) 提取
    %   原始数据集的MFCC特征
    % 输入参数:
    %   rawDataset - 原始音频数据集
    %   fs - 采样频率
    %   win - 窗函数
    %   overlapLength - 窗函数重叠长度
    % 输出参数:
    %   features - 提取的MFCC特征

    features = {}; 
    for i = 1:size(rawDataset, 1)
        S = stft(rawDataset(i,:)', "Window", win, "OverlapLength", overlapLength, "Centered", false);
        y_train = mfcc(S, fs, "LogEnergy", "Ignore");
        features{i, 1} = y_train;
    end
end

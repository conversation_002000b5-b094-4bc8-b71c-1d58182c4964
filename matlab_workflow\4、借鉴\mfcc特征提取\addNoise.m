function [ noisySample ] = addNoise(sample, noise, SNR )

%trim the noise file so that it's the same size as the sample
noise = noise(1:length(sample));

noisePower = calcPower(noise);
soundPower = calcPower(sample);

a = calcAlpha(soundPower, noisePower, SNR);

noiseScaled = noise * a;

noisySample = sample + noiseScaled;

end


% 上述代码定义了一个函数addNoise，它接受三个输入参数：sample是原始样本的音频数据，noise是噪声的音频数据，SNR是信噪比。
% 首先，通过length(sample)将噪声文件的长度调整为与样本相同。这是为了确保噪声和样本具有相同的采样点数。
% 接下来，通过调用calcPower函数计算噪声和样本的能量。calcPower函数可能是计算音频数据能量的自定义函数。
% 然后，通过调用calcAlpha函数计算一个放大因子a，用于调整噪声的强度。calcAlpha函数可能是根据信噪比和噪声能量与样本能量之间的关系来计算放大因子的自定义函数。
% 接下来，将噪声数据按照放大因子a进行缩放，得到noiseScaled。
% 最后，将缩放后的噪声数据noiseScaled和原始样本数据sample相加，得到添加噪声后的样本noisySample。
% 所以，这段代码的功能是将噪声添加到给定的样本中，以实现特定的信噪比。

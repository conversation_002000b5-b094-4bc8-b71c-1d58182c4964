function plot_bar_snr(snr_values, algorithm_names, title_text, fig_position)
    % 绘制信噪比柱状图并美化图形，支持图像大小和位置调整
    
    % 创建图形窗口并设置图像的大小和位置
    figure('Position', fig_position);
    
    bar(snr_values);
    
    % 设置字体、刻度和标签格式
    ax = gca;
    ax.FontSize = 16;  % 设置刻度文字大小
    ax.FontName = 'Times New Roman';  % 设置刻度字体
    ax.FontWeight = "bold";  % 设置刻度文字加粗
    ax.XTickLabel = algorithm_names;  % 设置X轴刻度标签为算法名称
    ax.XTickLabelRotation = 20;  % 旋转X轴刻度标签使其更容易阅读
    xlabel('Denoising algorithms', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('SNR (dB)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    
    % 使Y轴的范围自适应或手动设置
    % ax.YLim = [min(snr_values)-5, max(snr_values)+5];  % 如果需要固定Y轴范围，可以这样设置
    
    grid on;  % 打开网格以便更好地对比
end

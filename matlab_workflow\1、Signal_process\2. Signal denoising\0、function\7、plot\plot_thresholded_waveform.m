function plot_thresholded_waveform(t, data, title_text)
%PLOT_THRESHOLDED_WAVEFORM 阈值波形绘制函数
%   绘制小幅值范围的信号波形，专门用于显示降噪后的低幅值信号。
%   该函数提供精细的幅值范围显示，适用于评估降噪效果和信号质量。
%
%   语法:
%   plot_thresholded_waveform(t, data, title_text)
%
%   输入参数:
%   t          - 时间向量 (数值向量，单位：秒)
%   data       - 信号数据 (数值向量，归一化幅值)
%   title_text - 图形标题 (字符串)
%
%   输出参数:
%   无 - 直接生成图形显示
%
%   图形特征:
%   - Y轴范围: [-0.1, 0.1] (精细幅值显示)
%   - Y轴刻度: [-0.1, -0.05, 0, 0.05, 0.1]
%   - X轴范围: 自动适应时间向量范围
%   - 字体: Times New Roman, 加粗, 16pt刻度, 18pt标签
%
%   应用场景:
%   - 降噪后信号质量评估
%   - 小幅值信号细节观察
%   - 噪声残留检测
%   - 信号处理效果验证
%
%   示例:
%   % 基本用法
%   fs = 2570; % 采样率
%   t = (0:fs-1)/fs; % 1秒信号
%   denoised_signal = 0.02*sin(2*pi*50*t); % 小幅值信号
%   figure;
%   plot_thresholded_waveform(t, denoised_signal, '降噪后信号');
%
%   % 与标准波形对比
%   figure('Position', [100, 100, 1200, 400]);
%   subplot(1,2,1); plot_waveform_bs(t, original, '原始信号');
%   subplot(1,2,2); plot_thresholded_waveform(t, denoised, '降噪后信号');
%
%   注意事项:
%   - 适用于幅值范围在±0.1以内的信号
%   - 超出范围的信号将被截断显示
%   - 时间向量和数据向量长度必须相同
%   - 主要用于降噪后的精细观察
%
%   参见: PLOT_WAVEFORM_BS, PLOT_SPECTROGRAM_BS
%
%   作者: [作者姓名]
%   日期: [创建日期]
%   版本: 1.0

    % 输入参数验证
    if nargin < 3
        error('plot_thresholded_waveform:NotEnoughInputs', '需要三个输入参数');
    end

    if length(t) ~= length(data)
        error('plot_thresholded_waveform:DimensionMismatch', '时间向量和数据向量长度必须相同');
    end

    % 绘制波形
    plot(t, data); % 直接绘制波形

    % 设置图形格式
    ax = gca;
    ax.FontSize = 16; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = "bold";
    ax.YLim = [-0.1 0.1]; % 自定义纵坐标范围（精细显示）
    ax.XLim = [min(t) max(t)]; % 自动调整横坐标范围为时间序列的最小值和最大值
    yticks([-0.1 -0.05 0 0.05 0.1]); % 自定义纵坐标刻度

    % 设置标签和标题
    xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Normalized amplitude', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
end


% function plot_thresholded_waveform(t, data, threshold, title_text)
%     % 找到超出和未超出阈值的索引
%     overThresholdIndices = abs(data) > threshold;
%     underThresholdIndices = ~overThresholdIndices;
% 
%     % 绘制数据
%     hold on;
%     % 正常绘制未超出阈值的部分，使用实线
%     plot(t(underThresholdIndices), data(underThresholdIndices));
% 
%     % 用红色星号标注出超出阈值的部分，避免连线
%     plot(t(overThresholdIndices), data(overThresholdIndices), 'r*');
% 
%     hold off;
% 
%     % 设置绘图格式
%     ax = gca;
%     ax.FontSize = 16; % 设置刻度尺文字大小
%     ax.FontName = 'Times New Roman'; % 设置刻度尺字体
%     ax.FontWeight = "bold";
%     ax.YLim = [-0.1 0.1]; % 自定义纵坐标范围
%     ax.XLim = [min(t) max(t)]; % 自动调整横坐标范围为时间序列的最小值和最大值
%     yticks([-0.1 -0.05 0 0.05 0.1]); % 自定义纵坐标刻度
%     xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
%     ylabel('Amplitude', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
%     title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
%     legend('Within Threshold', 'Exceeding Threshold');
% 
%     % 恢复图形的所有边框
%     box on;
% end

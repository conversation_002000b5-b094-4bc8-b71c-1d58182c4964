function plot_waveform(t, data, position, rows, cols, index, title_text)
    figure('Position', position); % 设置图框位置和大小，[left, bottom, width, height]
    subplot(rows, cols, index);

    color1 = [56/255, 82/255, 151/255];
    color2 = [180/255, 115/255, 222/255];
    % plot(t, data,'Color', color1);
    plot(seconds(t), data,'Color', color1);
    ax = gca;
    ax.FontSize = 16; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = "bold";
    ax.YLim = [-1 1];
    ax.XLim = [0 300];
    yticks([-1 -0.5 0 0.5 1]); % 自定义纵坐标刻度
    xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Normalized amplitude', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
end
function [processedSignals, snrResults] = processDualChannelSignals(signals, config)
%PROCESSDUALCHANNELSIGNALS 双通道信号批量处理函数
%   对多个通道的信号进行统一的降噪处理，包括带通滤波和谱减法降噪。
%   该函数消除了原代码中的重复逻辑，提供了更高效和可维护的处理方案。
%
%   语法:
%   [processedSignals, snrResults] = processDualChannelSignals(signals, config)
%
%   输入参数:
%   signals - 输入信号数据 (结构体或元胞数组)
%             结构体格式: signals.channel1, signals.channel2, ...
%             元胞数组格式: {signal1, signal2, ...}
%   config  - 处理配置参数结构体 (由createProcessingConfig生成)
%
%   输出参数:
%   processedSignals - 处理后的信号数据 (与输入格式相同)
%   snrResults      - SNR计算结果结构体
%                     包含: bandpassSNR, spectralSubtractionSNR
%
%   处理流程:
%   1. 输入数据验证和格式统一
%   2. 数据预处理 (归一化、去直流、去趋势)
%   3. 带通滤波降噪
%   4. SNR计算 (带通滤波后)
%   5. 谱减法降噪 (可选)
%   6. SNR计算 (谱减法后)
%   7. 信号长度对齐处理
%
%   支持的信号格式:
%   - 双通道: {signal1, signal2}
%   - 多通道: {signal1, signal2, signal3, ...}
%   - 结构体: struct('ch1', signal1, 'ch2', signal2)
%
%   示例:
%   % 基本用法 - 双通道处理
%   config = createProcessingConfig();
%   signals = {column2, column3};
%   [processed, snr] = processDualChannelSignals(signals, config);
%   
%   % 结构体格式
%   inputSignals.channel1 = column2;
%   inputSignals.channel2 = column3;
%   [processed, snr] = processDualChannelSignals(inputSignals, config);
%   
%   % 访问结果
%   processedCh1 = processed{1};  % 或 processed.channel1
%   bandpassSNR1 = snr.bandpassSNR(1);
%   spectralSNR1 = snr.spectralSubtractionSNR(1);
%
%   性能优化:
%   - 向量化操作减少循环开销
%   - 统一的内存分配策略
%   - 可选的中间结果缓存
%   - 自适应的信号长度处理
%
%   错误处理:
%   - 输入数据格式验证
%   - 信号长度一致性检查
%   - 内存使用量监控
%   - 处理异常的优雅降级
%
%   注意事项:
%   - 所有输入信号长度必须相同
%   - 谱减法可能改变信号长度，函数会自动对齐
%   - 大信号处理时注意内存使用
%   - 建议在处理前验证配置参数
%
%   参见: CREATEPROCESSINGCONFIG, PREPROCESSDATA, SSBOLL79, SNR_SINGLECH
%
%   作者: [作者姓名]
%   日期: [创建日期]
%   版本: 1.0

    %% 输入参数验证
    if nargin < 2
        error('processDualChannelSignals:NotEnoughInputs', ...
            '需要两个输入参数：signals和config');
    end
    
    % 统一信号格式为元胞数组
    [signalArray, isStructInput] = normalizeSignalInput(signals);
    numChannels = length(signalArray);
    
    if config.enableVerboseOutput
        fprintf('开始处理 %d 个通道的信号...\n', numChannels);
    end
    
    %% 验证信号数据
    validateSignalData(signalArray, config);
    
    %% 初始化输出变量
    processedArray = cell(size(signalArray));
    snrResults = initializeSNRResults(numChannels);
    
    %% 批量处理每个通道
    for chIdx = 1:numChannels
        if config.enableProgressDisplay
            fprintf('  处理通道 %d/%d...\n', chIdx, numChannels);
        end
        
        % 获取当前通道信号
        currentSignal = signalArray{chIdx};
        originalLength = length(currentSignal);
        
        try
            % 步骤1: 数据预处理
            if config.enableVerboseOutput
                fprintf('    - 数据预处理...\n');
            end
            preprocessedSignal = preprocessSingleChannel(currentSignal, config);
            
            % 步骤2: 带通滤波
            if config.enableVerboseOutput
                fprintf('    - 带通滤波 (%.1f-%.1f Hz)...\n', ...
                    config.filterLowFreq, config.filterHighFreq);
            end
            filteredSignal = applyBandpassFilter(preprocessedSignal, config);
            
            % 步骤3: 计算带通滤波后的SNR
            if config.enableSNRCalculation
                snrResults.bandpassSNR(chIdx) = SNR_singlech(filteredSignal, preprocessedSignal);
                if config.enableVerboseOutput
                    fprintf('    - 带通滤波后 SNR = %.4f dB\n', snrResults.bandpassSNR(chIdx));
                end
            end
            
            % 步骤4: 谱减法降噪 (可选)
            if config.enableSpectralSubtraction
                if config.enableVerboseOutput
                    fprintf('    - 谱减法降噪...\n');
                end
                [spectralProcessedSignal, alignedLength] = applySpectralSubtraction(...
                    filteredSignal, originalLength, config);
                
                % 计算谱减法后的SNR
                if config.enableSNRCalculation
                    snrResults.spectralSubtractionSNR(chIdx) = SNR_singlech(...
                        spectralProcessedSignal, filteredSignal(1:alignedLength));
                    if config.enableVerboseOutput
                        fprintf('    - 谱减法后 SNR = %.4f dB\n', ...
                            snrResults.spectralSubtractionSNR(chIdx));
                    end
                end
                
                processedArray{chIdx} = spectralProcessedSignal;
            else
                processedArray{chIdx} = filteredSignal;
                snrResults.spectralSubtractionSNR(chIdx) = NaN;
            end
            
        catch ME
            if config.enableErrorHandling
                warning('processDualChannelSignals:ChannelProcessingFailed', ...
                    '通道 %d 处理失败: %s', chIdx, ME.message);
                % 使用原始信号作为备选
                processedArray{chIdx} = currentSignal;
                snrResults.bandpassSNR(chIdx) = NaN;
                snrResults.spectralSubtractionSNR(chIdx) = NaN;
            else
                rethrow(ME);
            end
        end
    end
    
    %% 转换输出格式
    if isStructInput
        processedSignals = convertArrayToStruct(processedArray, signals);
    else
        processedSignals = processedArray;
    end
    
    if config.enableVerboseOutput
        fprintf('所有通道处理完成。\n\n');
    end
end

function [signalArray, isStructInput] = normalizeSignalInput(signals)
%NORMALIZESIGNALINPUT 统一信号输入格式
    if isstruct(signals)
        fieldNames = fieldnames(signals);
        signalArray = cell(length(fieldNames), 1);
        for i = 1:length(fieldNames)
            signalArray{i} = signals.(fieldNames{i});
        end
        isStructInput = true;
    elseif iscell(signals)
        signalArray = signals(:);
        isStructInput = false;
    else
        error('processDualChannelSignals:InvalidInputFormat', ...
            '输入信号必须是元胞数组或结构体');
    end
end

function validateSignalData(signalArray, config)
%VALIDATESIGNALDATA 验证信号数据有效性
    numChannels = length(signalArray);
    
    if numChannels == 0
        error('processDualChannelSignals:EmptyInput', '输入信号为空');
    end
    
    % 检查信号长度一致性
    signalLengths = cellfun(@length, signalArray);
    if length(unique(signalLengths)) > 1
        error('processDualChannelSignals:InconsistentLength', ...
            '所有输入信号长度必须相同');
    end
    
    % 检查信号长度范围
    signalLength = signalLengths(1);
    if signalLength < config.minSignalLength
        error('processDualChannelSignals:SignalTooShort', ...
            '信号长度 (%d) 小于最小要求 (%d)', signalLength, config.minSignalLength);
    end
    
    if signalLength > config.maxSignalLength
        error('processDualChannelSignals:SignalTooLong', ...
            '信号长度 (%d) 超过最大限制 (%d)', signalLength, config.maxSignalLength);
    end
end

function snrResults = initializeSNRResults(numChannels)
%INITIALIZESNRRESULTS 初始化SNR结果结构体
    snrResults = struct();
    snrResults.bandpassSNR = NaN(numChannels, 1);
    snrResults.spectralSubtractionSNR = NaN(numChannels, 1);
end

function processedSignal = preprocessSingleChannel(signal, config)
%PREPROCESSSINGLECHANNEL 单通道预处理
    processedSignal = signal(:);  % 确保为列向量
    
    if config.enableDCRemoval
        processedSignal = processedSignal - mean(processedSignal);
    end
    
    if config.enableDetrend
        processedSignal = detrend(processedSignal);
    end
    
    % 归一化处理
    switch config.normalizationMethod
        case 'fixed_factor'
            processedSignal = processedSignal / config.normalizationFactor;
        case 'max_abs'
            processedSignal = processedSignal / max(abs(processedSignal));
        otherwise
            warning('processDualChannelSignals:UnknownNormalizationMethod', ...
                '未知的归一化方法，使用max_abs方法');
            processedSignal = processedSignal / max(abs(processedSignal));
    end
end

function filteredSignal = applyBandpassFilter(signal, config)
%APPLYBANDPASSFILTER 应用带通滤波器
    try
        filteredSignal = bandpass(signal, ...
            [config.filterLowFreq, config.filterHighFreq], ...
            config.samplingRate);
    catch ME
        warning('processDualChannelSignals:FilterFailed', ...
            '带通滤波失败: %s，使用原始信号', ME.message);
        filteredSignal = signal;
    end
end

function [processedSignal, alignedLength] = applySpectralSubtraction(signal, originalLength, config)
%APPLYSPECTRALSUBTRACTION 应用谱减法降噪
    try
        switch config.spectralSubtractionMethod
            case 'SSBoll79'
                output = SSBoll79(signal, config.samplingRate, config.spectralSubtractionIS);
            case 'SSBoll79m_2'
                output = SSBoll79m_2(signal, config.samplingRate, ...
                    config.spectralSubtractionIS, config.spectralSubtractionAlpha);
            otherwise
                error('未知的谱减法方法: %s', config.spectralSubtractionMethod);
        end
        
        % 信号长度对齐
        outputLength = length(output);
        if outputLength < originalLength
            % 补零到原始长度
            processedSignal = [output; zeros(originalLength - outputLength, 1)];
            alignedLength = originalLength;
        else
            % 截断到原始长度
            processedSignal = output(1:originalLength);
            alignedLength = originalLength;
        end
        
    catch ME
        warning('processDualChannelSignals:SpectralSubtractionFailed', ...
            '谱减法处理失败: %s，使用滤波后信号', ME.message);
        processedSignal = signal;
        alignedLength = length(signal);
    end
end

function outputStruct = convertArrayToStruct(signalArray, originalStruct)
%CONVERTARRAYTOSTRUCT 将数组转换回结构体格式
    fieldNames = fieldnames(originalStruct);
    outputStruct = struct();
    for i = 1:length(fieldNames)
        outputStruct.(fieldNames{i}) = signalArray{i};
    end
end

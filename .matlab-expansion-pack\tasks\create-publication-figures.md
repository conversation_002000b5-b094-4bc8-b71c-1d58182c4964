# Create Publication-Quality Figures Task

## Purpose

Create high-quality, publication-ready figures using MATLAB that meet academic and professional publishing standards for journals, conferences, and technical reports.

## Process

### 1. Publication Requirements Analysis

**Target Publication Standards:**
- Identify specific journal or conference requirements
- Review figure size, resolution, and format specifications
- Understand color vs. grayscale requirements
- Check font and typography guidelines

**Content Requirements:**
- Determine required figure elements (titles, captions, labels)
- Identify data to be presented and key messages
- Plan figure layout and panel organization
- Consider space constraints and layout limitations

### 2. Figure Planning and Design

**Layout Design:**
- Plan single vs. multi-panel figures
- Design subplot arrangements and spacing
- Establish visual hierarchy and flow
- Plan for caption and label placement

**Style Specification:**
- Define color palette (publication-appropriate)
- Select fonts and text sizes
- Establish line weights and marker sizes
- Plan consistent styling across figure set

### 3. Data Preparation

**Data Selection and Processing:**
- Select representative and significant data
- Process data for optimal presentation
- Handle missing values and outliers appropriately
- Prepare statistical summaries and error estimates

**Quality Assurance:**
- Verify data accuracy and integrity
- Check for appropriate statistical representation
- Ensure reproducibility of results
- Validate against source data

### 4. MATLAB Implementation

**Figure Setup:**
- Configure figure size and resolution
- Set up publication-quality defaults
- Configure color management and rendering
- Establish consistent styling parameters

**Plot Creation:**
- Implement plots using appropriate MATLAB functions
- Apply publication-specific formatting
- Add statistical elements (error bars, significance indicators)
- Implement custom styling and annotations

### 5. Professional Formatting

**Typography and Labels:**
- Apply consistent font families and sizes
- Format axis labels and tick marks
- Add clear and informative titles
- Implement proper mathematical notation

**Visual Enhancement:**
- Optimize color choices for print and digital
- Ensure sufficient contrast and readability
- Apply professional styling and branding
- Remove unnecessary visual clutter

### 6. Quality Control and Validation

**Technical Validation:**
- Verify figure meets publication specifications
- Check resolution and file format requirements
- Validate color reproduction and accessibility
- Test printing and digital display quality

**Content Review:**
- Verify accuracy of data representation
- Check for clarity and interpretability
- Ensure compliance with ethical guidelines
- Review for potential misinterpretation

### 7. Export and Finalization

**File Preparation:**
- Export in required formats (EPS, PDF, TIFF, etc.)
- Optimize file sizes while maintaining quality
- Prepare both color and grayscale versions
- Create backup copies and version control

**Documentation:**
- Prepare detailed figure captions
- Document data sources and methods
- Create supplementary materials if needed
- Provide reproduction instructions

## Publication Standards

### Journal Requirements

**IEEE Standards:**
- Figure width: 3.5" (single column) or 7.16" (double column)
- Resolution: 300 DPI minimum for photos, 600 DPI for line art
- Fonts: Times New Roman, minimum 8pt
- File formats: EPS, PDF preferred

**Nature/Science Standards:**
- High resolution: 300 DPI minimum
- Color specifications: RGB for online, CMYK for print
- Font requirements: Arial or Helvetica, minimum 5pt
- File size limitations and compression guidelines

**Engineering Journals:**
- Technical diagram standards
- Measurement unit specifications
- Symbol and notation conventions
- Reference and citation requirements

### Conference Standards

**Academic Conferences:**
- Presentation slide formatting
- Poster figure requirements
- Digital display optimization
- Interactive element considerations

**Professional Conferences:**
- Industry-specific formatting
- Branding and logo requirements
- Commercial presentation standards
- Audience-appropriate styling

## MATLAB Implementation Techniques

### Figure Configuration
```matlab
% Publication-quality figure setup
figure('Units', 'inches', 'Position', [0 0 3.5 2.5]);
set(gcf, 'PaperPositionMode', 'auto');
set(gcf, 'Color', 'white');
set(gcf, 'Renderer', 'painters');
```

### Font and Typography
```matlab
% Consistent typography
set(groot, 'defaultAxesFontName', 'Times New Roman');
set(groot, 'defaultAxesFontSize', 10);
set(groot, 'defaultTextFontName', 'Times New Roman');
set(groot, 'defaultTextFontSize', 10);
```

### Color Management
```matlab
% Publication-appropriate colors
colors = [0 0.4470 0.7410;     % Blue
          0.8500 0.3250 0.0980; % Red
          0.9290 0.6940 0.1250; % Yellow
          0.4940 0.1840 0.5560]; % Purple
```

### Export Settings
```matlab
% High-quality export
print(gcf, 'figure1', '-depsc', '-r600');  % EPS format
print(gcf, 'figure1', '-dpdf', '-r600');   % PDF format
exportgraphics(gcf, 'figure1.png', 'Resolution', 300);
```

## Quality Criteria

### Technical Quality
- **Resolution**: Meets or exceeds publication requirements
- **Format Compliance**: Correct file formats and specifications
- **Color Accuracy**: Appropriate color reproduction
- **Typography**: Professional font usage and sizing

### Visual Quality
- **Clarity**: Clear and readable at publication size
- **Consistency**: Uniform styling across figure set
- **Professional Appearance**: Publication-ready presentation
- **Accessibility**: Colorblind-friendly and high contrast

### Content Quality
- **Accuracy**: Faithful representation of data
- **Completeness**: All necessary information included
- **Clarity**: Easy to interpret and understand
- **Significance**: Effectively communicates key findings

## Common Publication Figure Types

### Research Figures
- **Experimental Results**: Data plots with error bars
- **Comparative Analysis**: Before/after, treatment/control
- **Statistical Analysis**: Distributions, correlations, trends
- **Model Validation**: Predicted vs. observed, residuals

### Technical Figures
- **System Diagrams**: Block diagrams, flowcharts
- **Performance Metrics**: Benchmarking, optimization results
- **Engineering Drawings**: Technical specifications, schematics
- **Process Illustrations**: Step-by-step procedures

### Review and Summary Figures
- **Literature Synthesis**: Meta-analysis results
- **Conceptual Models**: Theoretical frameworks
- **Timeline Figures**: Historical development, roadmaps
- **Classification Schemes**: Taxonomies, hierarchies

## Integration Points

This task works with:
- publication-figure-checklist for quality validation
- figure-specification-tmpl for design documentation
- visualization-quality-checklist for general quality
- matlab-plotting-best-practices for technical implementation

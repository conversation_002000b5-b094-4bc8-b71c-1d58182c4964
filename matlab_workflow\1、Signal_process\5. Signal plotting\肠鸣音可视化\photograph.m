%% 音频数据可视化程序
% 功能：加载并可视化timetable格式的音频数据，生成波形图、频谱图和强度图
% 输入：用户选择的.mat文件（包含tt1变量的timetable格式音频数据）
% 输出：三种类型的可视化图表（波形图、频谱图、强度图）
% 处理步骤：
%   1. 通过文件对话框选择并加载.mat文件，验证timetable数据格式
%   2. 提取时间序列和信号数据，时间归零处理
%   3. 绘制原始音频波形图
%   4. 生成频谱图显示频域特征
%   5. 计算并显示信号强度变化图
% 应用场景：音频信号的快速可视化分析和数据质量检查
% 用于处理和可视化用户选择的.mat文件中timetable格式数据的脚本

% 清空工作空间并关闭所有图形窗口
clear all;
close all;

% 添加函数路径 - function文件夹在上级目录
addpath('../function');

% 加载数据文件 - 让用户选择文件
[filename, pathname] = uigetfile('*.mat', '请选择包含timetable数据的.mat文件');

% 检查用户是否取消了文件选择
if isequal(filename, 0)
    disp('用户取消了文件选择');
    return;
end

% 构建完整的文件路径并加载
fullpath = fullfile(pathname, filename);
fprintf('正在加载文件: %s\n', fullpath);
load(fullpath);

% 检查加载的数据中是否存在tt1
if ~exist('tt1', 'var')
    error('在数据文件中找不到tt1');
end

% 确保tt1是timetable格式
if ~istimetable(tt1)
    error('tt1不是timetable格式');
end

% 从timetable中提取时间和信号数据
time = tt1.Time - tt1.Time(1); % 将时间归零
signal = tt1.Variables; % 假设timetable只有一列数据

% 设置采样频率
fs = 2570; % 采样频率

% 绘制波形图
position = [200, 200, 1500, 900]; % [left, bottom, width, height]

plot_waveform(time, signal, position, 2, 1, 1, 'Raw data of Mic (Body)');

% 绘制频谱图
plot_spectrogram(signal, fs, position, 2, 1, 2, 'Spectrogram of Mic (Body)');

% 显示强度图
plot_intensity(time, signal, fs, position, 2, 1, 1, 'Intensity of Mic (Body)');





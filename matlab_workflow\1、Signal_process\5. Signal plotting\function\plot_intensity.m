function plot_intensity(t, signal, fs, position, rows, cols, index, title_text)
    % 计算声音强度
    w = 0.05; % 设置窗口宽度
    win = round(fs*w); % 计算窗口大小
    ov = round(fs*w*0.9); % 计算重叠部分
    nfft = round(fs*0.5); % 设置FFT点数
    [~,~,T_C,P_C] = spectrogram(signal,win,ov,nfft,fs); % 计算频谱图

    % 计算声音强度
    % 频率分辨率 = fs/nfft ≈ 2.57Hz
    % 100Hz对应索引 ≈ 39
    % 1000Hz对应索引 ≈ 389
    freq_range = 39:389;  % 对应约100-1000Hz
    I_BowelSound = sum(P_C(freq_range,:)); % 计算肠鸣音强度

    % 将T_C转换为与time相同的时间范围
    T_C_adjusted = T_C * (seconds(t(end)) - seconds(t(1))) / T_C(end);

    % 使用插值将强度值调整到与time相同的时间点
    I_BowelSound_interpolated = interp1(T_C_adjusted, I_BowelSound, seconds(t));

    % 显示强度图
    figure('Position', position);
    subplot(rows, cols, index);
    color1 = [56/255, 82/255, 151/255];
    plot(seconds(t), I_BowelSound_interpolated, 'Color', color1) % 绘制强度
    ax = gca;
    ax.FontSize = 16; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = "bold";
    ax.YLim = [0 0.025];
    ax.XLim = [0 300];
    xticks([0 50 100 150 200 250 300]); % 自定义纵坐标刻度
    xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Intensity', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    
end 
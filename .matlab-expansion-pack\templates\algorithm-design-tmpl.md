# {{algorithm_name}} - Algorithm Design Specification

[[LLM: This template guides the systematic design and documentation of algorithms. Focus on mathematical rigor, implementation clarity, and performance analysis. Present numbered options for algorithm types and approaches.]]

## Algorithm Overview

[[LLM: Begin by understanding the algorithm's purpose and context. Ask about the problem domain and requirements.]]

**Algorithm Name:** {{algorithm_name}}
**Algorithm Type:** {{algorithm_type}}
**Problem Domain:** {{problem_domain}}
**Designer:** {{designer}}
**Design Date:** {{design_date}}

### Problem Statement

[[LLM: Clearly define the problem the algorithm solves. Be specific about inputs, outputs, and constraints.]]

{{problem_statement}}

### Algorithm Purpose

{{algorithm_purpose}}

### Key Requirements

{{key_requirements}}

## Mathematical Foundation

[[LLM: Establish the mathematical basis for the algorithm. This section is crucial for algorithmic correctness and analysis.]]

### Mathematical Model

[[LLM: Present the mathematical formulation. Use appropriate notation and be precise about mathematical relationships.]]

**Problem Formulation:**
{{mathematical_formulation}}

**Variables and Parameters:**
{{variables_parameters}}

**Constraints:**
{{mathematical_constraints}}

**Objective Function:**
{{objective_function}}

### Theoretical Analysis

**Computational Complexity:**
- Time Complexity: {{time_complexity}}
- Space Complexity: {{space_complexity}}
- Best Case: {{best_case_complexity}}
- Average Case: {{average_case_complexity}}
- Worst Case: {{worst_case_complexity}}

**Convergence Analysis:**
{{convergence_analysis}}

**Stability Analysis:**
{{stability_analysis}}

## Algorithm Design

[[LLM: Present different algorithmic approaches based on the problem type. Use numbered options:
1. Iterative approaches
2. Recursive approaches
3. Divide and conquer
4. Dynamic programming
5. Greedy algorithms
6. Optimization-based approaches]]

### Algorithm Approach

**Selected Approach:** {{selected_approach}}
**Rationale:** {{approach_rationale}}

### High-Level Algorithm Description

[[LLM: Provide a clear, step-by-step description of the algorithm at a high level.]]

{{high_level_description}}

### Detailed Algorithm Steps

[[LLM: Break down the algorithm into detailed, implementable steps.]]

<<REPEAT section="step" count="{{step_count}}">>
**Step {{step_number}}:** {{step_name}}
- **Description:** {{step_description}}
- **Input:** {{step_input}}
- **Output:** {{step_output}}
- **Complexity:** {{step_complexity}}
<</REPEAT>>

### Pseudocode

[[LLM: Provide formal pseudocode that can be directly translated to MATLAB implementation.]]

```
{{pseudocode}}
```

## Implementation Specifications

[[LLM: Define specific implementation requirements for MATLAB.]]

### MATLAB Implementation Requirements

**Function Signature:**
```matlab
{{function_signature}}
```

**Input Specifications:**
{{input_specifications}}

**Output Specifications:**
{{output_specifications}}

**Data Types:**
{{data_types}}

### Implementation Considerations

**Vectorization Opportunities:**
{{vectorization_opportunities}}

**Memory Management:**
{{memory_management}}

**Numerical Stability:**
{{numerical_stability}}

**Error Handling:**
{{error_handling}}

## Performance Analysis

[[LLM: Analyze expected performance characteristics and optimization opportunities.]]

### Performance Characteristics

**Expected Performance:**
{{expected_performance}}

**Scalability:**
{{scalability_analysis}}

**Memory Usage:**
{{memory_usage}}

**Bottlenecks:**
{{performance_bottlenecks}}

### Optimization Strategies

{{optimization_strategies}}

### Benchmarking Plan

{{benchmarking_plan}}

## Validation and Testing

[[LLM: Define comprehensive testing and validation strategy.]]

### Test Cases

**Unit Tests:**
{{unit_tests}}

**Integration Tests:**
{{integration_tests}}

**Performance Tests:**
{{performance_tests}}

### Validation Strategy

**Correctness Validation:**
{{correctness_validation}}

**Performance Validation:**
{{performance_validation}}

**Robustness Testing:**
{{robustness_testing}}

### Test Data

**Synthetic Test Data:**
{{synthetic_test_data}}

**Real-world Test Data:**
{{realworld_test_data}}

**Edge Cases:**
{{edge_cases}}

## Implementation Guidelines

[[LLM: Provide specific guidance for MATLAB implementation.]]

### Coding Standards

**Naming Conventions:**
{{naming_conventions}}

**Documentation Requirements:**
{{documentation_requirements}}

**Code Structure:**
{{code_structure}}

### MATLAB Best Practices

**Vectorization Guidelines:**
{{vectorization_guidelines}}

**Memory Optimization:**
{{memory_optimization}}

**Performance Optimization:**
{{performance_optimization}}

## Risk Assessment

[[LLM: Identify potential risks and mitigation strategies.]]

### Technical Risks

{{technical_risks}}

### Performance Risks

{{performance_risks}}

### Implementation Risks

{{implementation_risks}}

### Mitigation Strategies

{{mitigation_strategies}}

## Documentation and Maintenance

[[LLM: Define documentation and maintenance requirements.]]

### Documentation Requirements

{{documentation_requirements}}

### Maintenance Considerations

{{maintenance_considerations}}

### Version Control

{{version_control}}

## Approval and Review

[[LLM: After completing the design, present for review and approval.]]

**Design Reviewed By:** {{reviewed_by}}
**Review Date:** {{review_date}}
**Approved By:** {{approved_by}}
**Approval Date:** {{approval_date}}
**Implementation Ready:** {{implementation_ready}}

---

[[LLM: After completing the algorithm design, suggest next steps:
1. Review mathematical foundation and correctness
2. Validate design approach and complexity analysis
3. Begin MATLAB implementation
4. Set up testing framework
5. Plan performance benchmarking]]

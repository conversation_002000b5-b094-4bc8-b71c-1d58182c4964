function plotWelchPSD(y, fs, windowSize, overlap, nfft)
    % plotWelchPSD - 计算并绘制音频信号的韦尔奇功率谱密度
    %
    % 语法: plotWelchPSD(y, fs, windowSize, overlap, nfft)
    %
    % 输入参数:
    %   y - 音频信号数据
    %   fs - 采样频率
    %   windowSize - 窗口函数的大小（整数）
    %   overlap - 重叠的样本数（整数）
    %   nfft - FFT点数（整数）

    % 计算韦尔奇功率谱密度
    window = hamming(windowSize); % 窗口函数
    noverlap = overlap; % 重叠的样本数
    [psd, freq] = pwelch(y, window, noverlap, nfft, fs);

    % 绘制功率谱密度图

    figure('Position', [300, 300, 900, 600]); % 设置图框位置和大小，[left, bottom, width, height]
    plot(freq, 10*log10(psd));
    
    ax = gca;
    ax.FontSize = 14; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = "bold";
    ax.XLim = [0 900];
    ax.YLim = [-120 -35];
    xlabel('Frequency (Hz)', 'FontSize', 16, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('PSD (dB/Hz)', 'FontSize', 16, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title('Welch’s power spectrum', 'FontSize', 16, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
end
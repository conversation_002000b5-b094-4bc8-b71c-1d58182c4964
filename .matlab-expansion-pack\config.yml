version: 1.0.0
markdownExploder: true

# MATLAB Expansion Pack Configuration
root: matlab-expansion-pack
IDE-FILE-RESOLUTION: Dependencies map to files as {root}/{type}/{name}.md where root="matlab-expansion-pack", type=folder (tasks/templates/checklists/utils), name=dependency name.

# MATLAB Environment Configuration
matlab:
  matlabInstallPath: null  # Auto-detect or user-specified
  requiredToolboxes:
    - Signal Processing Toolbox
    - Image Processing Toolbox
    - Statistics and Machine Learning Toolbox
    - Optimization Toolbox
    - Simulink
  optionalToolboxes:
    - Deep Learning Toolbox
    - Computer Vision Toolbox
    - Control System Toolbox
    - Financial Toolbox
  
# Agent Loading Configuration
matlabLoadAlwaysFiles:
  - data/matlab-best-practices.md
  - data/matlab-coding-standards.md
  - data/matlab-optimization-techniques.md
  - data/performance-benchmarks.md

# Debug and Logging
matlabDebugLog: .ai/matlab-debug-log.md
matlabPerformanceLog: .ai/matlab-performance-log.md
matlabTestResults: .ai/matlab-test-results.md

# File Management
matlabFileTypes:
  - .m      # MATLAB scripts and functions
  - .mat    # MATLAB data files
  - .fig    # MATLAB figure files
  - .mlx    # Live scripts
  - .slx    # Simulink models
  - .mdl    # Legacy Simulink models
  - .p      # Protected MATLAB files

# Quality Gates
qualityGates:
  syntaxCheck: true
  performanceCheck: true
  testCoverage: 80
  documentationCheck: true
  vectorizationCheck: true

# Performance Benchmarks
performanceBenchmarks:
  maxExecutionTime: 1000  # milliseconds
  maxMemoryUsage: 100     # MB
  minVectorizationRatio: 0.8
  
# Testing Configuration
testing:
  framework: matlab.unittest
  testDirectory: tests/
  coverageThreshold: 80
  performanceTests: true
  
# Documentation Standards
documentation:
  helpFormat: matlab
  exampleRequired: true
  seeAlsoRequired: true
  inputValidation: true
  outputDescription: true

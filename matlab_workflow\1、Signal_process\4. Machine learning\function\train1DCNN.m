function trainedNet = train1DCNN(audioData, labels, numClasses, numEpochs, miniBatchSize)
    % train1DCNN - 用于训练1D卷积神经网络的函数
    %
    % 输入参数：
    % audioData - 输入的音频波形数据，应该是一个cell数组，每个元素是一个音频波形
    % labels - 对应的分类标签，应该是categorical格式
    % numClasses - 类别数量
    % numEpochs - 最大训练周期数
    % miniBatchSize - mini-batch的大小
    %
    % 输出参数：
    % trainedNet - 训练后的神经网络

    % 定义网络结构
    layers = [
        sequenceInputLayer(1, 'Name', 'Input')  % 一维输入层

        % 第一层卷积：32个滤波器，卷积核大小为25
        convolution1dLayer(25, 32, 'Name', 'Conv1D_1', 'Padding', 'same')
        reluLayer('Name', 'ReLU_1')

        % 第二层卷积：24个滤波器，卷积核大小为50
        convolution1dLayer(50, 24, 'Name', 'Conv1D_2', 'Padding', 'same')
        reluLayer('Name', 'ReLU_2')

        % 第三层卷积：16个滤波器，卷积核大小为75
        convolution1dLayer(75, 16, 'Name', 'Conv1D_3', 'Padding', 'same')
        reluLayer('Name', 'ReLU_3')

        % 第四层卷积：8个滤波器，卷积核大小为100
        convolution1dLayer(100, 8, 'Name', 'Conv1D_4', 'Padding', 'same')
        reluLayer('Name', 'ReLU_4')

        % 展平层
        flattenLayer('Name', 'Flatten')

        % 全连接层
        fullyConnectedLayer(numClasses, 'Name', 'FC')

        % Softmax层
        softmaxLayer('Name', 'Softmax')

        % 分类层
        classificationLayer('Name', 'Output')];

    % 定义训练选项
    options = trainingOptions('adam', ...
        'MaxEpochs', numEpochs, ...           % 最大训练周期数
        'MiniBatchSize', miniBatchSize, ...   % mini-batch大小
        'InitialLearnRate', 0.001, ...        % 学习率
        'Shuffle', 'every-epoch', ...         % 每个周期打乱数据
        'Verbose', false, ...
        'Plots', 'training-progress');        % 显示训练进度

    % 训练网络
    trainedNet = trainNetwork(audioData, labels, layers, options);
end


%****************************************%

% 假设有音频数据和标签
audioData = {randn(1000,1), randn(1000,1), randn(1000,1)}; % 示例的3段音频
labels = categorical({'class1', 'class2', 'class3'});  % 示例的标签
numClasses = 3;  % 假设有3个分类
numEpochs = 20;  % 训练20个周期
miniBatchSize = 64;  % mini-batch大小

% 训练网络
trainedNet = train1DCNN(audioData, labels, numClasses, numEpochs, miniBatchSize);

%****************************************%
% 对新的音频数据进行分类
newAudioData = randn(1000,1);  % 示例的新音频波形
predictedLabel = classify(trainedNet, {newAudioData});
disp(predictedLabel);

# check-toolboxes

Verify MATLAB toolbox availability and provide alternatives when required toolboxes are missing.

## Purpose
Ensure all required MATLAB toolboxes are available and suggest alternatives or workarounds for missing dependencies.

## Inputs
- **requiredToolboxes** (optional): List of required toolboxes (from config.yml)
- **optionalToolboxes** (optional): List of optional toolboxes
- **projectPath** (optional): Path to scan for toolbox dependencies

## Process

### 1. System Toolbox Detection
```matlab
% Get installed toolboxes
installedToolboxes = ver;
toolboxNames = {installedToolboxes.Name};

% Check MATLAB version compatibility
matlabVersion = version('-release');
```

### 2. Required Toolbox Validation
- Check each required toolbox from configuration
- Verify version compatibility
- Report missing critical dependencies

### 3. Code Dependency Analysis
- Scan MATLAB files for toolbox-specific functions
- Identify implicit toolbox dependencies
- Cross-reference with available toolboxes

### 4. Alternative Suggestions
- Provide alternatives for missing toolboxes
- Suggest core MATLAB implementations
- Recommend third-party alternatives when appropriate

### 5. Compatibility Report
- Generate toolbox compatibility matrix
- Highlight version conflicts
- Provide upgrade/downgrade recommendations

## Outputs
- **availableToolboxes**: List of installed toolboxes with versions
- **missingToolboxes**: Required toolboxes not found
- **alternatives**: Suggested alternatives for missing toolboxes
- **compatibilityReport**: Detailed compatibility analysis

## Common Toolbox Alternatives

### Signal Processing Toolbox
- **Missing**: Use core MATLAB functions (fft, filter, conv)
- **Alternative**: Custom implementations for basic operations

### Image Processing Toolbox
- **Missing**: Use core MATLAB matrix operations
- **Alternative**: Basic image operations with imread/imwrite

### Statistics and Machine Learning Toolbox
- **Missing**: Implement basic statistics manually
- **Alternative**: Use core MATLAB statistical functions

### Optimization Toolbox
- **Missing**: Use fminsearch, fminbnd from core MATLAB
- **Alternative**: Implement gradient descent manually

## Success Criteria
- All required toolboxes identified
- Missing dependencies documented
- Alternatives provided for missing toolboxes
- No blocking toolbox conflicts

## Error Handling
- MATLAB version incompatible: Report and suggest upgrade
- License issues: Provide alternative solutions
- Network toolbox check failure: Use local detection only

## Integration
- Updates matlabDebugLog with toolbox status
- Integrates with startup sequence validation
- Supports both interactive and automated checking

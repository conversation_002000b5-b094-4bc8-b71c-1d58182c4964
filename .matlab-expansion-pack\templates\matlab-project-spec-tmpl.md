# {{project_name}} - MATLAB Project Specification

[[LLM: This template guides the creation of comprehensive MATLAB project specifications. Execute each section systematically, gathering detailed requirements and technical specifications. Use numbered options to present choices to users.]]

## Project Overview

[[LLM: Start by gathering basic project information. Ask about project goals, scope, and constraints.]]

**Project Name:** {{project_name}}
**Project Type:** {{project_type}}
**Start Date:** {{start_date}}
**Target Completion:** {{target_completion}}
**Project Lead:** {{project_lead}}

### Project Description

{{project_description}}

### Objectives and Goals

[[LLM: Present numbered options for different types of objectives:
1. Research and Development
2. Product Development
3. Data Analysis and Insights
4. Algorithm Development
5. System Modeling and Simulation
6. Custom/Other]]

**Primary Objectives:**
{{primary_objectives}}

**Success Criteria:**
{{success_criteria}}

**Key Performance Indicators:**
{{kpis}}

## Technical Requirements

[[LLM: Gather detailed technical specifications based on project type. Adapt questions based on whether this is algorithm development, data analysis, simulation, etc.]]

### MATLAB/Simulink Requirements

**MATLAB Version:** {{matlab_version}}
**Required Toolboxes:**
{{required_toolboxes}}

**Simulink Requirements:** {{simulink_requirements}}

### System Requirements

**Operating System:** {{operating_system}}
**Hardware Requirements:** {{hardware_requirements}}
**Memory Requirements:** {{memory_requirements}}
**Storage Requirements:** {{storage_requirements}}

### Performance Requirements

[[LLM: Ask about performance constraints and requirements specific to the project type.]]

**Computational Performance:** {{computational_performance}}
**Memory Efficiency:** {{memory_efficiency}}
**Real-time Constraints:** {{realtime_constraints}}
**Scalability Requirements:** {{scalability_requirements}}

## Functional Requirements

[[LLM: Based on project type, gather specific functional requirements. Use conditional content for different project types.]]

^^CONDITION: project_type == "algorithm_development"^^
### Algorithm Requirements

**Input Specifications:** {{algorithm_inputs}}
**Output Specifications:** {{algorithm_outputs}}
**Performance Metrics:** {{algorithm_performance}}
**Accuracy Requirements:** {{accuracy_requirements}}
**Computational Complexity:** {{computational_complexity}}
^^/CONDITION: project_type^^

^^CONDITION: project_type == "data_analysis"^^
### Data Analysis Requirements

**Data Sources:** {{data_sources}}
**Data Volume:** {{data_volume}}
**Analysis Types:** {{analysis_types}}
**Visualization Requirements:** {{visualization_requirements}}
**Reporting Requirements:** {{reporting_requirements}}
^^/CONDITION: project_type^^

^^CONDITION: project_type == "simulation"^^
### Simulation Requirements

**System to Model:** {{system_description}}
**Model Fidelity:** {{model_fidelity}}
**Simulation Scenarios:** {{simulation_scenarios}}
**Validation Requirements:** {{validation_requirements}}
**Real-time Requirements:** {{realtime_simulation}}
^^/CONDITION: project_type^^

### Core Functionality

{{core_functionality}}

### User Interface Requirements

{{ui_requirements}}

### Integration Requirements

{{integration_requirements}}

## Data Requirements

[[LLM: Gather information about data needs, sources, and management requirements.]]

### Data Sources

**Input Data:** {{input_data}}
**Data Formats:** {{data_formats}}
**Data Volume:** {{data_volume}}
**Data Quality Requirements:** {{data_quality}}

### Data Management

**Storage Requirements:** {{data_storage}}
**Backup and Recovery:** {{backup_requirements}}
**Data Security:** {{data_security}}
**Data Governance:** {{data_governance}}

## Quality and Testing Requirements

[[LLM: Define quality standards and testing approaches appropriate for the project type.]]

### Quality Standards

**Code Quality Standards:** {{code_quality_standards}}
**Documentation Standards:** {{documentation_standards}}
**Performance Standards:** {{performance_standards}}

### Testing Strategy

**Unit Testing:** {{unit_testing}}
**Integration Testing:** {{integration_testing}}
**Performance Testing:** {{performance_testing}}
**Validation Testing:** {{validation_testing}}

## Project Structure and Organization

[[LLM: Define project organization and team structure.]]

### Team Structure

**Project Roles:**
{{project_roles}}

**Responsibilities:**
{{responsibilities}}

### Project Organization

**Directory Structure:** {{directory_structure}}
**File Naming Conventions:** {{naming_conventions}}
**Version Control Strategy:** {{version_control}}
**Documentation Structure:** {{documentation_structure}}

## Timeline and Milestones

[[LLM: Create project timeline with key milestones and deliverables.]]

### Project Phases

<<REPEAT section="phase" count="{{phase_count}}">>
**Phase {{phase_number}}:** {{phase_name}}
- **Duration:** {{phase_duration}}
- **Objectives:** {{phase_objectives}}
- **Deliverables:** {{phase_deliverables}}
- **Success Criteria:** {{phase_success_criteria}}
<</REPEAT>>

### Key Milestones

{{key_milestones}}

### Critical Path

{{critical_path}}

## Risk Assessment and Mitigation

[[LLM: Identify potential risks and mitigation strategies.]]

### Technical Risks

{{technical_risks}}

### Resource Risks

{{resource_risks}}

### Schedule Risks

{{schedule_risks}}

### Mitigation Strategies

{{mitigation_strategies}}

## Deliverables and Documentation

[[LLM: Define all project deliverables and documentation requirements.]]

### Primary Deliverables

{{primary_deliverables}}

### Documentation Deliverables

{{documentation_deliverables}}

### Knowledge Transfer

{{knowledge_transfer}}

## Approval and Sign-off

[[LLM: After completing all sections, present the specification for review and approval.]]

**Specification Prepared By:** {{prepared_by}}
**Date:** {{preparation_date}}
**Review Status:** {{review_status}}
**Approved By:** {{approved_by}}
**Approval Date:** {{approval_date}}

---

[[LLM: After completing the specification, suggest next steps:
1. Review and refine the specification
2. Get stakeholder approval
3. Begin project planning and resource allocation
4. Set up project infrastructure and tools
5. Initiate project kickoff]]

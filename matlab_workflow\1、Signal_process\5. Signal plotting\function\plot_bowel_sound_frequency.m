function plot_bowel_sound_frequency(signal, fs, peak_times)
    % 肠鸣音频率计算和标注函数，并提取每分钟内肠鸣音的时间范围
    % 输入：
    %   signal - 输入信号
    %   fs - 采样频率
    %   peak_times - 每个峰值对应的时间刻度（由 peak_search 输出的）

    % 定义肠鸣音事件的时间阈值，1500毫秒 = 1.5秒
    time_threshold = 1.2;  % 单位为秒

    % 计算相邻峰值之间的时间间隔
    time_diffs = diff(peak_times);  % 相邻峰值的时间差

    % 计算整个信号的总时长
    total_duration = peak_times(end);  % 整个信号的时长（秒）

    % 按分钟划分时长
    num_minutes = ceil(total_duration / 60);  % 计算总共有多少分钟

    % 初始化每分钟肠鸣音次数的数组
    bowel_sounds_per_minute = zeros(1, num_minutes);
    event_times_per_minute = cell(1, num_minutes);  % 用于存储每分钟肠鸣音事件的时间段

    % 遍历每一分钟，计算对应的肠鸣音事件
    for minute_idx = 1:num_minutes
        % 定义该分钟的时间范围
        minute_start = (minute_idx - 1) * 60;  % 该分钟的开始时间
        minute_end = minute_idx * 60;  % 该分钟的结束时间
        
        % 获取在该分钟内的峰值时间
        minute_peaks = peak_times(peak_times >= minute_start & peak_times < minute_end);
        
        % 初始化事件时间段
        event_times = [];
        
        if length(minute_peaks) > 1
            % 初始化肠鸣音事件计数
            num_events = 0;
            event_start = minute_peaks(1);  % 第一个事件的开始时间
            
            % 逐个检测峰值，并将连续的相邻峰值合并成一个肠鸣音事件
            for peak_idx = 2:length(minute_peaks)
                if (minute_peaks(peak_idx) - minute_peaks(peak_idx - 1)) > time_threshold
                    % 如果相邻峰值时间差大于阈值，则认为这是一个新的事件
                    num_events = num_events + 1;  % 记录一个事件
                    event_end = minute_peaks(peak_idx - 1);  % 前一个峰值为事件的结束时间
                    
                    % 记录事件的时间段
                    event_times = [event_times; event_start, event_end];
                    
                    % 开始新的事件
                    event_start = minute_peaks(peak_idx);
                end
            end
            
            % 处理最后一个事件
            num_events = num_events + 1;
            event_end = minute_peaks(end);  % 最后一个峰值作为结束时间
            event_times = [event_times; event_start, event_end];

            % 记录该分钟的肠鸣音事件数量
            bowel_sounds_per_minute(minute_idx) = num_events;
            event_times_per_minute{minute_idx} = event_times;
        else
            % 如果该分钟只有一个峰值或没有峰值，肠鸣音次数为0或1
            bowel_sounds_per_minute(minute_idx) = length(minute_peaks);  % 若只有一个峰值，则记为1次
            if ~isempty(minute_peaks)
                event_times_per_minute{minute_idx} = [minute_peaks(1), minute_peaks(1)];
            end
        end
    end

    % 控制方框的高度，设为信号幅度的固定比例
    box_height = 0.4;  % 方框高度固定为 0.4，以适应 [-1, 1] 范围的信号

    % 调用 plot_waveform 绘制原始波形
    t = (0:length(signal)-1) / fs;  % 时间轴
    figure('Position', [300, 300, 1500, 900]);
    plot_waveform(t, signal, 2, 1, 1, 'Frequency of Bowel Sound');
    hold on;

    % 用虚线方框标注肠鸣音事件的时间段，方框的中心纵坐标在 X 轴上
    for minute_idx = 1:num_minutes
        % 获取该分钟的肠鸣音事件时间段
        event_times = event_times_per_minute{minute_idx};
        
        % 如果该分钟有事件，绘制时间段
        if ~isempty(event_times)
            for event_idx = 1:size(event_times, 1)
                % 获取事件的开始时间和结束时间
                event_start = event_times(event_idx, 1);
                event_end = event_times(event_idx, 2);
                
                % 用红色虚线框圈出事件时间段，方框的中心纵坐标在 X 轴上（y = 0）
                rectangle('Position', [event_start, -box_height/2, event_end-event_start, box_height], ...
                          'EdgeColor', 'r', 'LineStyle', '--');
            end
        end

        % 在每分钟的中间位置标注肠鸣音的次数
        minute_center = (minute_idx - 0.5) * 60;  % 每分钟的中间位置
        text(minute_center, 0.8, ['BS/min: ', num2str(bowel_sounds_per_minute(minute_idx))], ...
             'HorizontalAlignment', 'center', 'Color', 'b', 'FontSize', 10, 'FontWeight', 'bold');
    end
    
    hold off;

    % 显示每分钟的肠鸣音次数及时间范围
    for i = 1:num_minutes
        disp(['第', num2str(i), '分钟的肠鸣音次数: ', num2str(bowel_sounds_per_minute(i)), ' 次']);
        if ~isempty(event_times_per_minute{i})
            disp('肠鸣音事件时间段:');
            for j = 1:size(event_times_per_minute{i}, 1)
                disp(['  第', num2str(j), '次: ', num2str(event_times_per_minute{i}(j, 1)), '秒 到 ', num2str(event_times_per_minute{i}(j, 2)), '秒']);
            end
        end
    end
end




% function plot_bowel_sound_frequency(signal, fs, peak_times)
%     % 肠鸣音频率计算和标注函数，并提取每分钟内肠鸣音的时间范围
%     % 输入：
%     %   signal - 输入信号
%     %   fs - 采样频率
%     %   peak_times - 每个峰值对应的时间刻度（由 peak_search 输出的）
% 
%     % 定义肠鸣音事件的时间阈值，1500毫秒 = 1.5秒
%     time_threshold = 1.5;  % 单位为秒
% 
%     % 计算相邻峰值之间的时间间隔
%     time_diffs = diff(peak_times);  % 相邻峰值的时间差
% 
%     % 计算整个信号的总时长
%     total_duration = peak_times(end);  % 整个信号的时长（秒）
% 
%     % 按分钟划分时长
%     num_minutes = ceil(total_duration / 60);  % 计算总共有多少分钟
% 
%     % 初始化每分钟肠鸣音次数的数组
%     bowel_sounds_per_minute = zeros(1, num_minutes);
%     event_times_per_minute = cell(1, num_minutes);  % 用于存储每分钟肠鸣音事件的时间段
% 
%     % 遍历每一分钟，计算对应的肠鸣音事件
%     for minute_idx = 1:num_minutes
%         % 定义该分钟的时间范围
%         minute_start = (minute_idx - 1) * 60;  % 该分钟的开始时间
%         minute_end = minute_idx * 60;  % 该分钟的结束时间
% 
%         % 获取在该分钟内的峰值时间
%         minute_peaks = peak_times(peak_times >= minute_start & peak_times < minute_end);
% 
%         % 初始化事件时间段
%         event_times = [];
% 
%         if length(minute_peaks) > 1
%             % 初始化肠鸣音事件计数
%             num_events = 0;
%             event_start = minute_peaks(1);  % 第一个事件的开始时间
% 
%             % 逐个检测峰值，并将连续的相邻峰值合并成一个肠鸣音事件
%             for peak_idx = 2:length(minute_peaks)
%                 if (minute_peaks(peak_idx) - minute_peaks(peak_idx - 1)) > time_threshold
%                     % 如果相邻峰值时间差大于阈值，则认为这是一个新的事件
%                     num_events = num_events + 1;  % 记录一个事件
%                     event_end = minute_peaks(peak_idx - 1);  % 前一个峰值为事件的结束时间
% 
%                     % 记录事件的时间段
%                     event_times = [event_times; event_start, event_end];
% 
%                     % 开始新的事件
%                     event_start = minute_peaks(peak_idx);
%                 end
%             end
% 
%             % 处理最后一个事件
%             num_events = num_events + 1;
%             event_end = minute_peaks(end);  % 最后一个峰值作为结束时间
%             event_times = [event_times; event_start, event_end];
% 
%             % 记录该分钟的肠鸣音事件数量
%             bowel_sounds_per_minute(minute_idx) = num_events;
%             event_times_per_minute{minute_idx} = event_times;
%         else
%             % 如果该分钟只有一个峰值或没有峰值，肠鸣音次数为0或1
%             bowel_sounds_per_minute(minute_idx) = length(minute_peaks);  % 若只有一个峰值，则记为1次
%             if ~isempty(minute_peaks)
%                 event_times_per_minute{minute_idx} = [minute_peaks(1), minute_peaks(1)];
%             end
%         end
%     end
% 
%     % 控制方框的高度，设为信号幅度的固定比例
%     box_height = 0.4;  % 方框高度固定为 0.4，以适应 [-1, 1] 范围的信号
% 
%     % 调用 plot_waveform 绘制原始波形
%     t = (0:length(signal)-1) / fs;  % 时间轴
%     figure('Position', [300, 300, 1500, 900]);
%     plot_waveform(t, signal, 2, 1, 1, 'Frequency of Bowel Sound');
%     hold on;
% 
%     % 用虚线方框标注肠鸣音事件的时间段，方框的中心纵坐标在 X 轴上
%     for minute_idx = 1:num_minutes
%         % 获取该分钟的肠鸣音事件时间段
%         event_times = event_times_per_minute{minute_idx};
% 
%         % 如果该分钟有事件，绘制时间段
%         if ~isempty(event_times)
%             for event_idx = 1:size(event_times, 1)
%                 % 获取事件的开始时间和结束时间
%                 event_start = event_times(event_idx, 1);
%                 event_end = event_times(event_idx, 2);
% 
%                 % 用红色虚线框圈出事件时间段，方框的中心纵坐标在 X 轴上（y = 0）
%                 rectangle('Position', [event_start, -box_height/2, event_end-event_start, box_height], ...
%                           'EdgeColor', 'r', 'LineStyle', '--');
%             end
%         end
%     end
% 
%     hold off;
% 
%     % 显示每分钟的肠鸣音次数及时间范围
%     for i = 1:num_minutes
%         disp(['第', num2str(i), '分钟的肠鸣音次数: ', num2str(bowel_sounds_per_minute(i)), ' 次']);
%         if ~isempty(event_times_per_minute{i})
%             disp('肠鸣音事件时间段:');
%             for j = 1:size(event_times_per_minute{i}, 1)
%                 disp(['  第', num2str(j), '次: ', num2str(event_times_per_minute{i}(j, 1)), '秒 到 ', num2str(event_times_per_minute{i}(j, 2)), '秒']);
%             end
%         end
%     end
% end
% 

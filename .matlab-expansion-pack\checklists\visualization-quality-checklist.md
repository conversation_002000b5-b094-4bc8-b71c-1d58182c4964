# Visualization Quality Checklist

[[LLM: This checklist provides comprehensive quality assessment for MATLAB visualizations. Work through each section systematically, evaluating design quality, technical implementation, and effectiveness.]]

## Required Artifacts

- MATLAB visualization code and scripts
- Generated figures and plots
- Data sources and datasets
- Design specifications and requirements
- Output files in various formats

## Section 1: Design and Aesthetics (Weight: 25%)

[[LLM: Evaluate the visual design quality and aesthetic appeal of the visualization.]]

### 1.1 Visual Clarity
- [ ] Information is clearly presented and easy to understand
- [ ] Visual hierarchy guides viewer attention effectively
- [ ] Important elements are properly emphasized
- [ ] Unnecessary visual clutter is eliminated

### 1.2 Color Usage
- [ ] Color palette is appropriate and professional
- [ ] Colors enhance rather than distract from the message
- [ ] Colorblind-friendly palette is used
- [ ] Sufficient contrast for readability

### 1.3 Typography and Labels
- [ ] Fonts are readable and appropriately sized
- [ ] Text labels are clear and informative
- [ ] Mathematical notation is properly formatted
- [ ] Consistent typography throughout

### 1.4 Layout and Composition
- [ ] Elements are well-balanced and organized
- [ ] Appropriate use of white space
- [ ] Logical flow and visual progression
- [ ] Professional and polished appearance

**Section 1 Rating:** ⭐⭐⭐⭐⭐ (1-5 stars)
**Section 1 Comments:** {{section_1_comments}}

## Section 2: Data Representation and Accuracy (Weight: 30%)

[[LLM: Assess the accuracy and appropriateness of data representation.]]

### 2.1 Data Integrity
- [ ] Data is accurately represented without distortion
- [ ] Scales and proportions are appropriate
- [ ] No misleading visual elements
- [ ] Statistical measures are correctly displayed

### 2.2 Chart Type Appropriateness
- [ ] Visualization type is suitable for the data
- [ ] Chart effectively communicates the intended message
- [ ] Alternative chart types were considered
- [ ] Best practices for chart type are followed

### 2.3 Axis and Scale Configuration
- [ ] Axes are properly labeled and scaled
- [ ] Origin and scale choices are appropriate
- [ ] Tick marks and gridlines enhance readability
- [ ] Units and measurements are clearly indicated

### 2.4 Statistical Elements
- [ ] Error bars and confidence intervals are included where appropriate
- [ ] Statistical significance is properly indicated
- [ ] Uncertainty is appropriately communicated
- [ ] Sample sizes and statistical methods are documented

**Section 2 Rating:** ⭐⭐⭐⭐⭐ (1-5 stars)
**Section 2 Comments:** {{section_2_comments}}

## Section 3: Technical Implementation (Weight: 20%)

[[LLM: Evaluate the technical quality of the MATLAB implementation.]]

### 3.1 Code Quality
- [ ] MATLAB code is well-structured and readable
- [ ] Appropriate plotting functions are used
- [ ] Code follows MATLAB best practices
- [ ] Custom functions are properly implemented

### 3.2 Performance and Efficiency
- [ ] Visualization renders efficiently
- [ ] Memory usage is optimized
- [ ] Large datasets are handled appropriately
- [ ] Interactive elements are responsive

### 3.3 Customization and Styling
- [ ] Consistent styling is applied
- [ ] Custom styling enhances the visualization
- [ ] Style parameters are well-organized
- [ ] Reusable style templates are used

### 3.4 Export and Output Quality
- [ ] High-quality output in required formats
- [ ] Resolution is appropriate for intended use
- [ ] Vector formats are used when appropriate
- [ ] File sizes are optimized

**Section 3 Rating:** ⭐⭐⭐⭐⭐ (1-5 stars)
**Section 3 Comments:** {{section_3_comments}}

## Section 4: Usability and Accessibility (Weight: 15%)

[[LLM: Assess the usability and accessibility of the visualization.]]

### 4.1 User Experience
- [ ] Visualization is intuitive and easy to interpret
- [ ] Interactive elements are user-friendly
- [ ] Navigation and controls are clear
- [ ] Loading times are acceptable

### 4.2 Accessibility Standards
- [ ] Colorblind accessibility is ensured
- [ ] Sufficient contrast ratios are maintained
- [ ] Alternative text descriptions are provided
- [ ] Screen reader compatibility is considered

### 4.3 Cross-Platform Compatibility
- [ ] Visualization displays correctly across platforms
- [ ] Different screen sizes are accommodated
- [ ] Print versions are legible and accurate
- [ ] Web compatibility is ensured (if applicable)

### 4.4 Documentation and Help
- [ ] Clear instructions for interpretation are provided
- [ ] Interactive help or tooltips are available
- [ ] Data sources and methods are documented
- [ ] Contact information for questions is provided

**Section 4 Rating:** ⭐⭐⭐⭐⭐ (1-5 stars)
**Section 4 Comments:** {{section_4_comments}}

## Section 5: Communication Effectiveness (Weight: 10%)

[[LLM: Evaluate how effectively the visualization communicates its intended message.]]

### 5.1 Message Clarity
- [ ] Key message is immediately apparent
- [ ] Supporting details enhance understanding
- [ ] Conclusions are well-supported by the visualization
- [ ] Audience needs are addressed

### 5.2 Context and Background
- [ ] Appropriate context is provided
- [ ] Background information supports interpretation
- [ ] Comparisons and benchmarks are included
- [ ] Temporal or spatial context is clear

### 5.3 Storytelling and Narrative
- [ ] Visualization tells a coherent story
- [ ] Logical progression of information
- [ ] Key insights are highlighted
- [ ] Call-to-action or next steps are clear

### 5.4 Target Audience Appropriateness
- [ ] Complexity level matches audience expertise
- [ ] Terminology is appropriate for audience
- [ ] Cultural and contextual considerations are addressed
- [ ] Format suits intended use case

**Section 5 Rating:** ⭐⭐⭐⭐⭐ (1-5 stars)
**Section 5 Comments:** {{section_5_comments}}

## Overall Assessment

[[LLM: Calculate overall score and provide comprehensive summary.]]

**Overall Visualization Quality Score:** {{overall_score}}/5 ⭐
**Weighted Score Calculation:**
- Section 1 (25%): {{section_1_score}} × 0.25 = {{section_1_weighted}}
- Section 2 (30%): {{section_2_score}} × 0.30 = {{section_2_weighted}}
- Section 3 (20%): {{section_3_score}} × 0.20 = {{section_3_weighted}}
- Section 4 (15%): {{section_4_score}} × 0.15 = {{section_4_weighted}}
- Section 5 (10%): {{section_5_score}} × 0.10 = {{section_5_weighted}}

**Total Weighted Score:** {{total_weighted_score}}

## Key Findings

### Visualization Strengths
{{visualization_strengths}}

### Areas for Improvement
{{improvement_areas}}

### Critical Issues
{{critical_issues}}

## Detailed Assessment

### Design Quality
{{design_quality_assessment}}

### Technical Quality
{{technical_quality_assessment}}

### Communication Effectiveness
{{communication_effectiveness}}

### Accessibility and Usability
{{accessibility_assessment}}

## Recommendations

### Immediate Improvements
{{immediate_improvements}}

### Design Enhancements
{{design_enhancements}}

### Technical Optimizations
{{technical_optimizations}}

### Long-term Recommendations
{{longterm_recommendations}}

## Publication Readiness

### Academic Publication
- [ ] Meets journal formatting requirements
- [ ] Appropriate resolution and file format
- [ ] Professional appearance and quality
- [ ] Proper citations and attributions

### Professional Presentation
- [ ] Suitable for target audience
- [ ] Clear and impactful message
- [ ] High-quality output format
- [ ] Consistent with brand guidelines

### Web and Digital Use
- [ ] Optimized for digital display
- [ ] Interactive elements function properly
- [ ] Mobile-friendly design
- [ ] Fast loading and responsive

## Quality Gate Decision

[[LLM: Make a clear recommendation based on the visualization assessment.]]

**Visualization Quality Status:** {{quality_status}}
- ✅ **APPROVED** - Visualization meets all quality standards and is ready for use
- ⚠️ **CONDITIONAL APPROVAL** - Visualization is acceptable with minor improvements
- ❌ **REJECTED** - Visualization requires significant improvements before acceptance

**Justification:** {{quality_justification}}

**Next Steps:** {{next_steps}}

## Certification

**Reviewed By:** {{reviewer_name}}
**Review Date:** {{review_date}}
**Design Approval:** {{design_approval}}
**Technical Approval:** {{technical_approval}}
**Final Approval:** {{final_approval}}

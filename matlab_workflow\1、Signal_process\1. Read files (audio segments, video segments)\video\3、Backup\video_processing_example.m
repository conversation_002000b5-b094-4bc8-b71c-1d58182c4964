%% 视频分割处理工具使用示例
% 本脚本演示如何使用不同的视频分割模式处理视频文件
% 
% 作者: 视频处理模块
% 日期: 2025-08-21
% 版本: 1.0

%% 清理工作空间
clear; clc; close all;

%% 配置参数
% 输入和输出文件夹路径
inputFolder = 'input';      % 原始视频文件夹
outputFolder = 'output';    % 处理后视频保存文件夹

% 创建必要的文件夹
if ~exist(inputFolder, 'dir')
    mkdir(inputFolder);
    fprintf('已创建输入文件夹: %s\n', inputFolder);
    fprintf('请将待处理的视频文件放入此文件夹中\n');
end

if ~exist(outputFolder, 'dir')
    mkdir(outputFolder);
    fprintf('已创建输出文件夹: %s\n', outputFolder);
end

%% 检查输入文件
videoFiles = [dir(fullfile(inputFolder, '*.mp4')); ...
              dir(fullfile(inputFolder, '*.avi')); ...
              dir(fullfile(inputFolder, '*.mov'))];

if isempty(videoFiles)
    fprintf('\n警告: 在输入文件夹中未找到视频文件！\n');
    fprintf('支持的格式: .mp4, .avi, .mov\n');
    fprintf('请将视频文件放入 %s 文件夹后重新运行\n', inputFolder);
    return;
end

fprintf('\n找到 %d 个视频文件:\n', length(videoFiles));
for i = 1:length(videoFiles)
    fprintf('  %d. %s\n', i, videoFiles(i).name);
end

%% 选择分割模式
fprintf('\n=== 视频分割模式选择 ===\n');
fprintf('1. 9秒模式  - 33个9秒片段 + 1个3秒片段 (总计约5分钟/周期)\n');
fprintf('2. 18秒模式 - 16个18秒片段 + 1个12秒片段 (总计约5分钟/周期)\n');
fprintf('3. 24秒模式 - 16个24秒片段 + 1个5秒片段 (总计约6.5分钟/周期)\n');
fprintf('4. 60秒模式 - 6个60秒片段 + 1个29秒片段 (总计约6.5分钟/周期)\n');

while true
    choice = input('\n请选择分割模式 (1-4): ');
    if ismember(choice, [1, 2, 3, 4])
        break;
    else
        fprintf('无效选择，请输入1-4之间的数字\n');
    end
end

%% 执行相应的分割函数
fprintf('\n开始处理视频...\n');
tic; % 开始计时

switch choice
    case 1
        fprintf('使用9秒分割模式...\n');
        run('splitvideo_9.m');
        
    case 2
        fprintf('使用18秒分割模式...\n');
        run('splitvideo_18.m');
        
    case 3
        fprintf('使用24秒分割模式...\n');
        run('splitvideo_24.m');
        
    case 4
        fprintf('使用60秒分割模式...\n');
        run('splitvideo_60.m');
end

processingTime = toc; % 结束计时

%% 处理完成统计
fprintf('\n=== 处理完成 ===\n');
fprintf('总处理时间: %.2f 秒 (%.2f 分钟)\n', processingTime, processingTime/60);

% 统计输出文件
outputFiles = dir(fullfile(outputFolder, '*.mp4'));
if ~isempty(outputFiles)
    fprintf('成功生成 %d 个视频片段\n', length(outputFiles));
    
    % 计算总文件大小
    totalSize = sum([outputFiles.bytes]);
    if totalSize > 1e9
        fprintf('输出文件总大小: %.2f GB\n', totalSize/1e9);
    elseif totalSize > 1e6
        fprintf('输出文件总大小: %.2f MB\n', totalSize/1e6);
    else
        fprintf('输出文件总大小: %.2f KB\n', totalSize/1e3);
    end
    
    fprintf('\n输出文件保存在: %s\n', fullfile(pwd, outputFolder));
else
    fprintf('警告: 未找到输出文件，处理可能失败\n');
end

%% 显示使用建议
fprintf('\n=== 使用建议 ===\n');
fprintf('1. 建议在处理大文件前先备份原始视频\n');
fprintf('2. 确保有足够的磁盘空间存储分割后的文件\n');
fprintf('3. 可以根据需要调整不同的分割模式\n');
fprintf('4. 处理完成后可以删除不需要的片段以节省空间\n');

fprintf('\n处理完成！\n');

function [ noisePower ] = calcPower( signal )
%UNTITLED2 Summary of this function goes here
%   Detailed explanation goes here
noisePower = 0.0;
for n = 1 : length(signal)
    noisePower = noisePower + signal(n)^2;
end
noisePower = noisePower / (length(signal) - 1000);

end

这段代码是一个用于计算信号噪声功率的函数。下面是对代码的解释：

% 第1行定义了一个函数calcPower，它接受一个信号数组作为输入，并返回一个表示噪声功率的变量noisePower。
% 第4行到第6行是对该函数的简单描述，用于提供函数的摘要和详细解释。
% 第8行初始化噪声功率noisePower为0.0。
% 第9行开始一个for循环，从1迭代到信号数组的长度，用变量n表示每个迭代步骤。
% 第10行到第12行的循环体中，噪声功率noisePower被累加计算，每一步都将信号数组中第n个元素的平方加到noisePower上。
% 第14行通过除以信号数组的长度减去1000来归一化噪声功率的计算结果。
% 第16行结束了函数，并返回最终的噪声功率值给调用者。

% 总的来说，这段代码的作用是计算信号数组的噪声功率。通过对信号数组的每个元素求平方并累加，
% 然后除以信号数组长度减去1000来归一化噪声功率的计算结果。
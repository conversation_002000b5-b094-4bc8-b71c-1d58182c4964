function peelingEnergy = calculatePeelingEnergy(force, displacement)
    % 检查是否有 NaN 数据或全零数据
    if any(isnan(force)) || any(isnan(displacement))
        error('Force or displacement data contains NaN values');
    end

    if all(force == 0) || all(displacement == 0)
        error('Force or displacement data contains only zeros');
    end

    % 计算基底的圆面积，直径为5厘米
    diameter = 5;  % 单位为厘米
    radius = diameter / 2;
    substrateArea = pi * radius^2;  % 基底的面积，单位为平方厘米

    % 将位移从 mm 转换为 m
    displacement_m = displacement * 1e-3;  % mm 转换为 m

    % 使用 trapz 计算力-位移曲线下的面积（即剥离能量的数值积分）
    workDone = trapz(displacement_m, force);  % 剥离能量，单位为 N·m

    % 将剥离能量除以基底面积，得到单位面积的剥离能量，单位为 N/cm^2
    peelingEnergy_cm2 = workDone / substrateArea;

    % 单位转换：从 N/cm^2 转换为 J/m^2 (1 N/cm^2 = 10^4 J/m^2)
    peelingEnergy = peelingEnergy_cm2 * 1e4;
end




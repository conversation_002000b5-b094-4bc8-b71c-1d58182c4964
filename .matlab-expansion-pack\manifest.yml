name: matlab-expansion-pack
version: 1.0.0
description: >-
  Comprehensive MATLAB development toolkit with specialized AI agents for programming,
  algorithm design, simulation modeling, and data analysis. Provides structured workflows,
  quality assurance, and best practices for MATLAB/Simulink development projects.
author: PhD Research Team
bmad_version: "4.0.0"

# Files created in the expansion pack
files:
  config:
    - config.yml                 # MATLAB expansion pack configuration (dev-agent inspired)

  agents:
    - matlab-orchestrator.md      # Dr<PERSON> <PERSON> - Senior MATLAB Technical Lead
    - matlab-programmer.md        # <PERSON> - MATLAB Programming Specialist
    - algorithm-designer.md       # Dr<PERSON> <PERSON> - <PERSON>gorithm Development Expert
    - simulation-modeler.md       # <PERSON> - Simulink Modeling Specialist
    - data-analyst.md            # <PERSON> - MATLAB Data Analysis Expert
    - visualization-specialist.md # <PERSON><PERSON> <PERSON> - MATLAB Visualization & Graphics Expert

  data:
    - matlab-best-practices.md    # MATLAB programming best practices and conventions
    - algorithm-design-patterns.md # Common algorithmic patterns and implementations
    - simulink-modeling-guidelines.md # Simulink modeling standards and practices
    - matlab-optimization-techniques.md # Performance optimization strategies
    - numerical-methods-reference.md # Mathematical and numerical methods reference
    - matlab-plotting-best-practices.md # MATLAB visualization and graphics best practices

  tasks:
    # Core utilities (REQUIRED - copied from bmad-core)
    - create-doc.md              # Document creation from templates
    - execute-checklist.md       # Checklist validation system
    # MATLAB-specific tasks (dev-agent inspired)
    - run-matlab-tests.md        # Execute MATLAB unit tests and performance benchmarks
    - check-toolboxes.md         # Verify MATLAB toolbox availability and alternatives
    - profile-code.md            # Run MATLAB profiler and performance analysis
    - code-review-matlab.md      # Comprehensive MATLAB code review
    - design-simulink-model.md   # Simulink model design and implementation
    - analyze-data-matlab.md     # MATLAB data analysis and visualization
    - debug-matlab-code.md       # MATLAB debugging and troubleshooting
    - optimize-performance.md    # Performance analysis and optimization
    - validate-algorithm.md      # Algorithm validation and verification
    - design-visualization.md    # Data visualization design and implementation
    - create-publication-figures.md # Publication-quality figure creation

  utils:
    # Core utilities (REQUIRED - copied from bmad-core)
    - template-format.md         # Template markup conventions
    - workflow-management.md     # Workflow orchestration system

  templates:
    - matlab-project-spec-tmpl.md # Comprehensive MATLAB project specification
    - algorithm-design-tmpl.md   # Algorithm design and documentation template
    - simulink-model-spec-tmpl.md # Simulink model specification template
    - matlab-code-review-tmpl.md # Code review documentation template
    - data-analysis-report-tmpl.md # Data analysis report template
    - visualization-guide-tmpl.md # Visualization design guide template

  checklists:
    - matlab-code-quality-checklist.md # Multi-level MATLAB code quality assessment
    - algorithm-validation-checklist.md # Algorithm correctness and performance validation
    - simulink-model-checklist.md # Simulink model quality and validation
    - data-analysis-checklist.md # Data analysis quality and statistical validity
    - visualization-quality-checklist.md # Visualization design and quality assessment

  workflows:
    - matlab-development-workflow.md # Primary MATLAB development workflow with decision trees

  agent-teams:
    - matlab-team.yml           # MATLAB team configuration with coordination protocols

# Data files users must provide (in their bmad-core/data/ directory)
required_user_data:
  - filename: matlab-coding-standards.md
    description: Organization-specific MATLAB coding guidelines and conventions
    format: Markdown document with coding rules, naming conventions, and style guidelines
    example: |
      # MATLAB Coding Standards
      ## Naming Conventions
      - Variables: camelCase (e.g., dataMatrix, resultVector)
      - Functions: camelCase with descriptive names
      - Constants: UPPER_CASE
    validation: Should include sections on naming, formatting, documentation, and quality standards

  - filename: project-requirements.md
    description: Current project specifications, constraints, and objectives
    format: Markdown document with project details, requirements, and success criteria
    example: |
      # Project Requirements
      ## Objectives
      - Develop signal processing algorithm for real-time analysis
      ## Constraints
      - Must run in real-time (< 10ms processing time)
      - Memory usage < 100MB
    validation: Should specify functional requirements, performance constraints, and acceptance criteria

  - filename: performance-benchmarks.md
    description: Performance targets, measurement criteria, and benchmarking standards
    format: Markdown document with performance metrics and measurement procedures
    example: |
      # Performance Benchmarks
      ## Computational Performance
      - Algorithm execution time: < 1ms for 1000 data points
      ## Memory Usage
      - Peak memory usage: < 50MB
    validation: Should include quantitative performance targets and measurement methodologies

# Knowledge base files embedded in expansion pack
embedded_knowledge:
  - matlab-best-practices.md
  - algorithm-design-patterns.md
  - simulink-modeling-guidelines.md
  - matlab-optimization-techniques.md
  - numerical-methods-reference.md

# Dependencies on core BMAD components
core_dependencies:
  agents:
    - dev              # For general programming support
    - architect        # For system design and architecture
    - qa               # For quality assurance processes
  tasks:
    - create-doc       # Document generation from templates
    - execute-checklist # Quality validation processes
  workflows:
    - brownfield-service # For existing system integration
    - greenfield-service # For new system development

# Agent interaction patterns
agent_coordination:
  orchestrator: matlab-orchestrator
  handoff_protocols: true
  numbered_options: true
  quality_integration: comprehensive
  workflow_management: structured

# Specialized capabilities
capabilities:
  algorithm_development:
    - Mathematical modeling and analysis
    - Algorithm design and optimization
    - Complexity analysis and validation
    - Performance benchmarking and optimization
    
  simulation_modeling:
    - Simulink model design and implementation
    - System modeling and analysis
    - Model validation and verification
    - Real-time simulation and testing
    
  data_analysis:
    - Statistical analysis and modeling
    - Data visualization and reporting
    - Machine learning and predictive modeling
    - Exploratory data analysis
    
  code_development:
    - MATLAB programming and optimization
    - Code review and quality assurance
    - Performance profiling and optimization
    - Testing and validation frameworks

# Quality assurance framework
quality_framework:
  multi_level_validation:
    - Basic: Functional correctness and basic quality
    - Comprehensive: Performance, maintainability, and documentation
    - Expert: Advanced optimization, scalability, and best practices
    
  star_rating_system:
    - Code quality assessment (1-5 stars)
    - Performance evaluation (1-5 stars)
    - Documentation completeness (1-5 stars)
    - Overall project quality (1-5 stars)
    
  ready_not_ready_gates:
    - Design readiness assessment
    - Implementation quality gates
    - Deployment readiness validation

# Post-install message
post_install_message: |
  🎯 MATLAB Expansion Pack Ready!

  🧙‍♂️ ORCHESTRATOR: Dr. Alex Chen (matlab-orchestrator)
  Senior MATLAB Technical Lead for project coordination and workflow management

  👥 SPECIALIST AGENTS:
  💻 Sarah Rodriguez (matlab-programmer) - MATLAB Programming Specialist
  🧮 Dr. Michael Zhang (algorithm-designer) - Algorithm Development Expert
  🔧 Jennifer Park (simulation-modeler) - Simulink Modeling Specialist
  📊 David Kumar (data-analyst) - MATLAB Data Analysis Expert

  📋 CAPABILITIES:
  ✅ Algorithm development and optimization
  ✅ Simulink model design and simulation
  ✅ Data analysis and visualization
  ✅ Code quality assurance and optimization
  ✅ Multi-level quality validation with star ratings

  📁 REQUIRED USER DATA FILES (place in bmad-core/data/):
  - matlab-coding-standards.md: Organization coding guidelines and conventions
  - project-requirements.md: Project specifications and constraints
  - performance-benchmarks.md: Performance targets and measurement criteria

  🚀 QUICK START:
  1. Add required data files to bmad-core/data/
  2. Run: npm run agent matlab-orchestrator
  3. Follow Dr. Alex Chen's numbered options for project coordination
  4. Leverage specialist agents for specific MATLAB development tasks

  🎓 EMBEDDED KNOWLEDGE:
  - MATLAB programming best practices and optimization techniques
  - Algorithm design patterns and numerical methods
  - Simulink modeling guidelines and system design principles
  - Quality standards and validation frameworks
  - Structured workflows with handoff protocols and quality gates

  💡 ADVANCED FEATURES:
  - LLM template system with intelligent document generation
  - Workflow orchestration with decision trees and conditional paths
  - Character consistency across all agent interactions
  - Comprehensive quality integration at every development stage

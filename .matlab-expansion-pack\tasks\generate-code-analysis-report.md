# Generate Code Analysis Report Task

## Purpose

Create a comprehensive Markdown report (`code_analysis_report.md`) that documents the complete analysis of the MATLAB workspace, including project overview, file descriptions, quality assessment, and usage guidelines.

## Process

### 1. Report Structure Planning

**Report Sections:**
1. Project Overview and Summary
2. Directory Structure and Organization
3. File Inventory and Descriptions
4. Data Files and Dependencies
5. Code Quality Assessment
6. Usage Instructions and Workflow
7. Dependency Relationships
8. Recommendations and Improvements
9. Appendices (detailed metrics, checklists)

### 2. Project Overview Section

**Content Requirements:**
- Project title and purpose description
- Main objectives and scope
- Key algorithms and methodologies used
- Target audience and use cases
- System requirements and dependencies

**Structure:**
```markdown
# Project Analysis Report

## Project Overview
- **Project Name**: [Extracted from workspace or user input]
- **Purpose**: [Comprehensive description of project goals]
- **Domain**: [Scientific/Engineering field]
- **Complexity Level**: [Beginner/Intermediate/Advanced]
- **Last Updated**: [Analysis date]

## Summary Statistics
- Total MATLAB files: X
- Total data files: Y
- Lines of code: Z
- Documentation coverage: N%
- Quality score: M/5
```

### 3. Directory Structure Documentation

**Hierarchical Structure:**
- Visual directory tree representation
- File organization patterns
- Naming conventions used
- Logical grouping assessment

**Example Format:**
```markdown
## Directory Structure
```
workspace/
├── main_analysis.m          # Main entry point
├── functions/
│   ├── data_processing/
│   │   ├── load_data.m      # Data loading utilities
│   │   └── clean_data.m     # Data preprocessing
│   └── analysis/
│       ├── statistical_analysis.m
│       └── visualization.m
├── data/
│   ├── raw_data.mat
│   └── processed_data.mat
└── results/
    ├── figures/
    └── output_data/
```
```

### 4. File Inventory and Descriptions

**Per-File Documentation:**
For each .m file, include:
- File type (function, script, class)
- Primary purpose and functionality
- Input/output parameters
- Key algorithms or methods used
- Dependencies and relationships
- Quality metrics and scores
- Documentation status

**Template Format:**
```markdown
### filename.m
- **Type**: Function/Script/Class
- **Purpose**: [Detailed description]
- **Inputs**: [Parameter descriptions]
- **Outputs**: [Return value descriptions]
- **Algorithm**: [Key methods/approaches]
- **Dependencies**: [Required files/toolboxes]
- **Quality Score**: X/5
- **Documentation**: Complete/Partial/Missing
- **Notes**: [Special considerations]
```

### 5. Data Files Documentation

**Data Inventory:**
- File formats and sizes
- Content descriptions and structure
- Usage patterns and dependencies
- Data quality and completeness
- Processing requirements

**Data Flow Mapping:**
- Input data sources
- Processing pipeline stages
- Intermediate data products
- Final output formats
- Storage and archival considerations

### 6. Code Quality Assessment

**Quality Metrics Summary:**
- Overall quality score calculation
- Complexity analysis results
- Maintainability assessment
- Performance evaluation
- Standards compliance check

**Quality Breakdown:**
```markdown
## Code Quality Assessment

### Overall Metrics
- **Average Quality Score**: X.X/5.0
- **Code Complexity**: Low/Medium/High
- **Documentation Coverage**: XX%
- **Test Coverage**: XX%
- **Performance Rating**: Excellent/Good/Needs Improvement

### Quality by Category
| Category | Score | Comments |
|----------|-------|----------|
| Readability | X/5 | [Assessment notes] |
| Maintainability | X/5 | [Assessment notes] |
| Performance | X/5 | [Assessment notes] |
| Documentation | X/5 | [Assessment notes] |
| Error Handling | X/5 | [Assessment notes] |
```

### 7. Usage Instructions and Workflow

**Getting Started Guide:**
- System requirements and setup
- Required MATLAB toolboxes
- Installation and configuration steps
- Initial data preparation

**Execution Workflow:**
- Step-by-step usage instructions
- Main entry points and scripts
- Parameter configuration guidance
- Expected outputs and results

**Example Usage:**
```markdown
## Usage Instructions

### Prerequisites
- MATLAB R20XXa or later
- Required Toolboxes: [List]
- Data files: [Requirements]

### Quick Start
1. Set MATLAB path to workspace directory
2. Load required data: `load('data/input_data.mat')`
3. Run main analysis: `results = main_analysis(parameters)`
4. View results: `visualize_results(results)`

### Detailed Workflow
[Step-by-step instructions with code examples]
```

### 8. Dependency Relationships

**Dependency Mapping:**
- Function call hierarchies
- Data dependencies
- Toolbox requirements
- External library dependencies

**Visualization:**
- Dependency graphs (text-based or mermaid)
- Critical path identification
- Modularity assessment
- Coupling analysis

### 9. Recommendations and Improvements

**Priority-Ranked Suggestions:**
- High priority: Critical issues requiring immediate attention
- Medium priority: Quality improvements and optimizations
- Low priority: Enhancement opportunities and best practices

**Improvement Categories:**
- Code quality enhancements
- Performance optimizations
- Documentation improvements
- Testing and validation needs
- Modularity and maintainability upgrades

### 10. Report Generation Workflow

**Data Collection:**
- Aggregate analysis results from all tasks
- Compile quality metrics and assessments
- Gather file descriptions and metadata
- Collect dependency information

**Content Generation:**
- Generate markdown sections systematically
- Include code examples and usage patterns
- Create tables and structured data presentations
- Add cross-references and navigation aids

**Quality Assurance:**
- Verify all sections are complete
- Check for consistency and accuracy
- Validate code examples and instructions
- Ensure proper markdown formatting

**Final Assembly:**
- Combine all sections into single document
- Add table of contents and navigation
- Include appendices and supplementary information
- Generate final `code_analysis_report.md`

## Report Quality Standards

**Comprehensive (5/5):**
- All sections complete and detailed
- Accurate technical information
- Clear usage instructions
- Actionable recommendations
- Professional presentation

**Thorough (4/5):**
- Most sections complete
- Generally accurate information
- Adequate usage guidance
- Useful recommendations
- Good presentation quality

**Adequate (3/5):**
- Basic sections present
- Some technical accuracy
- Minimal usage instructions
- Limited recommendations
- Acceptable presentation

**Incomplete (2/5):**
- Missing key sections
- Questionable accuracy
- Poor usage guidance
- Vague recommendations
- Poor presentation

**Insufficient (1/5):**
- Minimal content
- Inaccurate information
- No usage instructions
- No recommendations
- Unprofessional presentation

## Integration Points

This task synthesizes results from:
- analyze-workspace-code task
- enhance-matlab-comments task
- assess-code-quality task
- code-review-matlab task

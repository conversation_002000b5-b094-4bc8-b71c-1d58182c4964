# Data Analysis Quality Checklist

[[LLM: This checklist provides comprehensive quality assessment for data analysis projects. Work through each section systematically, evaluating methodology, statistical validity, and interpretation quality.]]

## Required Artifacts

- Data analysis code and scripts
- Raw and processed datasets
- Analysis results and outputs
- Statistical reports and visualizations
- Documentation and methodology

## Section 1: Data Quality and Preparation (Weight: 25%)

[[LLM: Evaluate the quality of data and data preparation procedures.]]

### 1.1 Data Source and Collection
- [ ] Data sources are reliable and appropriate
- [ ] Data collection methodology is sound
- [ ] Sample size is adequate for analysis objectives
- [ ] Data collection period is appropriate

### 1.2 Data Quality Assessment
- [ ] Data completeness is evaluated and documented
- [ ] Data accuracy is verified where possible
- [ ] Data consistency is checked across sources
- [ ] Missing data patterns are analyzed

### 1.3 Data Preprocessing
- [ ] Data cleaning procedures are appropriate
- [ ] Missing value treatment is justified
- [ ] Outlier detection and treatment are documented
- [ ] Data transformations are appropriate and documented

### 1.4 Data Validation
- [ ] Data integrity checks are performed
- [ ] Data ranges and distributions are validated
- [ ] Cross-validation with external sources is performed
- [ ] Data lineage and provenance are documented

**Section 1 Rating:** ⭐⭐⭐⭐⭐ (1-5 stars)
**Section 1 Comments:** {{section_1_comments}}

## Section 2: Methodology and Statistical Validity (Weight: 30%)

[[LLM: Assess the appropriateness and validity of analytical methods used.]]

### 2.1 Analysis Design
- [ ] Analysis approach is appropriate for research questions
- [ ] Statistical methods are correctly selected
- [ ] Experimental design is sound (if applicable)
- [ ] Confounding variables are identified and controlled

### 2.2 Statistical Assumptions
- [ ] Statistical assumptions are tested and validated
- [ ] Assumption violations are identified and addressed
- [ ] Alternative methods are considered when assumptions fail
- [ ] Robustness of results is evaluated

### 2.3 Hypothesis Testing
- [ ] Hypotheses are clearly stated and testable
- [ ] Significance levels are appropriate and pre-specified
- [ ] Multiple testing corrections are applied when needed
- [ ] Effect sizes are reported alongside p-values

### 2.4 Model Validation
- [ ] Model selection procedures are appropriate
- [ ] Cross-validation or holdout testing is performed
- [ ] Model assumptions are validated
- [ ] Model performance metrics are appropriate

**Section 2 Rating:** ⭐⭐⭐⭐⭐ (1-5 stars)
**Section 2 Comments:** {{section_2_comments}}

## Section 3: Results and Interpretation (Weight: 20%)

[[LLM: Evaluate the quality of results presentation and interpretation.]]

### 3.1 Results Presentation
- [ ] Results are clearly and accurately presented
- [ ] Statistical measures are appropriately reported
- [ ] Confidence intervals and uncertainty are provided
- [ ] Results tables and figures are well-formatted

### 3.2 Statistical Interpretation
- [ ] Statistical significance is correctly interpreted
- [ ] Practical significance is discussed
- [ ] Causal vs. correlational relationships are distinguished
- [ ] Limitations and caveats are acknowledged

### 3.3 Effect Sizes and Magnitude
- [ ] Effect sizes are reported and interpreted
- [ ] Clinical or practical significance is assessed
- [ ] Results are placed in appropriate context
- [ ] Magnitude of findings is clearly communicated

### 3.4 Uncertainty and Variability
- [ ] Uncertainty in estimates is quantified
- [ ] Variability sources are identified
- [ ] Sensitivity analysis is performed
- [ ] Robustness of conclusions is evaluated

**Section 3 Rating:** ⭐⭐⭐⭐⭐ (1-5 stars)
**Section 3 Comments:** {{section_3_comments}}

## Section 4: Visualization and Communication (Weight: 15%)

[[LLM: Assess the quality of data visualization and communication of findings.]]

### 4.1 Data Visualization
- [ ] Visualizations are appropriate for data types
- [ ] Charts and graphs are clear and informative
- [ ] Visual design principles are followed
- [ ] Misleading visualizations are avoided

### 4.2 Statistical Graphics
- [ ] Statistical plots accurately represent data
- [ ] Uncertainty is visualized appropriately
- [ ] Multiple comparisons are handled correctly
- [ ] Interactive visualizations enhance understanding

### 4.3 Communication Clarity
- [ ] Findings are communicated clearly to target audience
- [ ] Technical jargon is explained or avoided
- [ ] Key insights are highlighted effectively
- [ ] Actionable recommendations are provided

### 4.4 Reproducibility
- [ ] Analysis code is well-documented and organized
- [ ] Results can be reproduced from provided code
- [ ] Data processing steps are clearly documented
- [ ] Software versions and dependencies are specified

**Section 4 Rating:** ⭐⭐⭐⭐⭐ (1-5 stars)
**Section 4 Comments:** {{section_4_comments}}

## Section 5: Documentation and Reporting (Weight: 10%)

[[LLM: Evaluate the quality and completeness of documentation and reporting.]]

### 5.1 Methodology Documentation
- [ ] Analysis methodology is thoroughly documented
- [ ] Statistical procedures are clearly described
- [ ] Software and tools used are specified
- [ ] Parameter settings and configurations are recorded

### 5.2 Results Documentation
- [ ] All results are properly documented
- [ ] Intermediate steps and decisions are recorded
- [ ] Failed analyses and negative results are reported
- [ ] Supplementary materials are provided

### 5.3 Quality Assurance
- [ ] Peer review or independent verification is performed
- [ ] Code review and validation are conducted
- [ ] Results are cross-checked and validated
- [ ] Quality control procedures are documented

### 5.4 Ethical Considerations
- [ ] Data privacy and confidentiality are protected
- [ ] Ethical guidelines are followed
- [ ] Potential biases are acknowledged
- [ ] Conflicts of interest are disclosed

**Section 5 Rating:** ⭐⭐⭐⭐⭐ (1-5 stars)
**Section 5 Comments:** {{section_5_comments}}

## Overall Assessment

[[LLM: Calculate overall score and provide comprehensive summary.]]

**Overall Analysis Quality Score:** {{overall_score}}/5 ⭐
**Weighted Score Calculation:**
- Section 1 (25%): {{section_1_score}} × 0.25 = {{section_1_weighted}}
- Section 2 (30%): {{section_2_score}} × 0.30 = {{section_2_weighted}}
- Section 3 (20%): {{section_3_score}} × 0.20 = {{section_3_weighted}}
- Section 4 (15%): {{section_4_score}} × 0.15 = {{section_4_weighted}}
- Section 5 (10%): {{section_5_score}} × 0.10 = {{section_5_weighted}}

**Total Weighted Score:** {{total_weighted_score}}

## Key Findings

### Analysis Strengths
{{analysis_strengths}}

### Areas for Improvement
{{improvement_areas}}

### Critical Issues
{{critical_issues}}

## Validation Results

### Data Quality Assessment
{{data_quality_results}}

### Statistical Validity
{{statistical_validity_results}}

### Interpretation Quality
{{interpretation_quality_results}}

### Reproducibility Assessment
{{reproducibility_results}}

## Recommendations

### Immediate Actions Required
{{immediate_actions}}

### Methodological Improvements
{{methodological_improvements}}

### Documentation Enhancements
{{documentation_enhancements}}

### Long-term Recommendations
{{longterm_recommendations}}

## Quality Gate Decision

[[LLM: Make a clear recommendation based on the analysis assessment.]]

**Analysis Quality Status:** {{quality_status}}
- ✅ **APPROVED** - Analysis meets all quality standards and findings are reliable
- ⚠️ **CONDITIONAL APPROVAL** - Analysis is acceptable with minor improvements
- ❌ **REJECTED** - Analysis requires significant improvements before acceptance

**Justification:** {{quality_justification}}

**Next Steps:** {{next_steps}}

## Certification

**Reviewed By:** {{reviewer_name}}
**Review Date:** {{review_date}}
**Statistical Review:** {{statistical_review}}
**Quality Approval:** {{quality_approval}}

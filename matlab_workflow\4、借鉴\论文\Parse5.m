function xp = Parse5(Data,sampling)

% Data = Data(1:1:end-sampling+1,:);


% 计算 B 中相邻元素的差，并将第一个元素设置为 0
A = (0:sampling:length(Data)-1);
B = Data(1:sampling:end-sampling+1,1);

d_t_5 = B(2:1:end,1)-B(1:1:end-1,1);
d_t_5 = [0;d_t_5];

% 这个循环用于处理 B 中的负差值
for i = 1:1:length(d_t_5)
    if d_t_5(i)<0 
        if i ~= length(d_t_5)
            B(i) = (B(i+1)+B(i-1))/2;
        else 
            B(i) = B(i-1)+(B(i-1)-B(i-2));           
        end 
    end 
end

% 将修正后的 B 值替换回 Data 的第一列
for i = 1:1:length(B)
Data(1+sampling*(i-1),1)= B(i);
end

% 用于对 Data 的第一列进行线性插值。对于每个采样段（块），它通过计算前后采样段之间的差值并按步长进行线性插值来填补间隙。
for i = 1:1:length(Data(1:1:end-sampling+1,1))/sampling
    for j = 1:1:sampling-1
        Data((i-1)*sampling+j+1,1)=(Data((i)*sampling+1,1)-Data((i-1)*sampling+1,1))/sampling*j+Data((i-1)*sampling+1,1);
    end 
end 


% F_Data1：提取 Data 第一列，从第 1 行到倒数第 sampling 行。
% F_Data2：提取 Data 从第 sampling+1 行开始的所有列，直到倒数第 sampling 行。
F_Data1 = Data(1:1:end-sampling+1-sampling,1);
F_Data2 = Data(1+sampling:1:end-sampling+1,2:end);

xp = [F_Data1,F_Data2]; % filtfilt for phase correction
%zero phase forward and backwasrds filter.

# 性能优化指南

## 概述
本文档提供了针对肠鸣音信号标注数据集处理代码的性能优化建议和实施方案。

## 已识别的性能问题

### 1. 动态数组扩展问题
**位置**: `demo_usage_4.m` 多个位置
**问题**: 在循环中使用 `array = [array; newData]` 导致频繁的内存重新分配

**影响**:
- 时间复杂度从O(n)增加到O(n²)
- 内存碎片化
- 处理大数据集时性能显著下降

### 2. 具体问题位置

#### 问题1: 幅值数据拼接 (已修复)
```matlab
% 原始代码 (性能差)
allAmplitudes = [];
for j = 1:length(labelSignals)
    allAmplitudes = [allAmplitudes; labelSignals{j}];
end

% 优化后代码 (性能好)
totalLength = sum(cellfun(@length, labelSignals));
allAmplitudes = zeros(totalLength, 1);
currentIdx = 1;
for j = 1:length(labelSignals)
    signalLength = length(labelSignals{j});
    allAmplitudes(currentIdx:currentIdx+signalLength-1) = labelSignals{j};
    currentIdx = currentIdx + signalLength;
end
```

#### 问题2: 分组标签构建
**位置**: 第200行
```matlab
% 当前代码 (需要优化)
group_labels = [group_labels, repmat({label}, 1, sum(labelIndices))];

% 建议优化
totalLabels = sum(cellfun(@sum, {labelIndices}));
group_labels = cell(1, totalLabels);
currentIdx = 1;
for i = 1:length(uniqueLabels)
    label = uniqueLabels{i};
    labelIndices = strcmp(allData.labels, label);
    count = sum(labelIndices);
    group_labels(currentIdx:currentIdx+count-1) = repmat({label}, 1, count);
    currentIdx = currentIdx + count;
end
```

#### 问题3: 幅值分布数据准备
**位置**: 第216行
```matlab
% 当前代码 (需要优化)
allAmps = [];
for j = 1:length(labelSignals)
    allAmps = [allAmps; labelSignals{j}];
end

% 建议优化
totalLength = sum(cellfun(@length, labelSignals));
allAmps = zeros(totalLength, 1);
currentIdx = 1;
for j = 1:length(labelSignals)
    signalLength = length(labelSignals{j});
    allAmps(currentIdx:currentIdx+signalLength-1) = labelSignals{j};
    currentIdx = currentIdx + signalLength;
end
```

## 优化策略

### 1. 内存预分配原则
- 在循环前计算所需的总大小
- 使用 `zeros()`, `cell()` 等函数预分配内存
- 避免在循环中改变数组大小

### 2. 向量化操作
```matlab
% 替代显式循环
lengths = cellfun(@length, labelSignals);
totalLength = sum(lengths);
```

### 3. 批量处理
```matlab
% 批量字符串比较
labelIndices = strcmp(allData.labels, label);
% 批量数值操作
meanValues = cellfun(@mean, labelSignals);
```

## 实施建议

### 高优先级修复
1. **修复demo_usage_4.m中的动态数组问题**
   - 第200行: group_labels构建
   - 第216行: allAmps拼接
   - 第239行: segments_by_label赋值

### 中优先级优化
1. **向量化改进**
   - 使用cellfun减少显式循环
   - 批量处理相似操作

### 低优先级增强
1. **并行处理**
   - 对独立的文件处理使用parfor
   - 利用多核处理器优势

## 性能测试建议

### 基准测试
```matlab
% 测试代码性能
tic;
% 原始代码
originalTime = toc;

tic;
% 优化代码
optimizedTime = toc;

fprintf('性能提升: %.2fx\n', originalTime/optimizedTime);
```

### 内存使用监控
```matlab
% 监控内存使用
memBefore = memory;
% 执行代码
memAfter = memory;
memUsed = memBefore.MemAvailableAllArrays - memAfter.MemAvailableAllArrays;
fprintf('内存使用: %.2f MB\n', memUsed/1024/1024);
```

## 预期性能提升

### 小数据集 (< 100个文件)
- 时间提升: 2-3倍
- 内存减少: 30-50%

### 中等数据集 (100-1000个文件)
- 时间提升: 5-10倍
- 内存减少: 50-70%

### 大数据集 (> 1000个文件)
- 时间提升: 10-50倍
- 内存减少: 70-90%

## 实施计划

### 第一阶段 (立即实施)
- [ ] 修复demo_usage_4.m中的动态数组问题
- [ ] 添加性能测试代码
- [ ] 验证优化效果

### 第二阶段 (1-2周内)
- [ ] 实施向量化改进
- [ ] 添加内存使用监控
- [ ] 更新文档

### 第三阶段 (1个月内)
- [ ] 评估并行处理可行性
- [ ] 实施批量处理优化
- [ ] 性能基准测试

## 注意事项

1. **向后兼容性**: 确保优化不影响现有功能
2. **测试覆盖**: 每次优化后运行完整测试
3. **文档更新**: 及时更新相关文档
4. **渐进式改进**: 分步骤实施，避免引入新问题

## 联系信息
如有性能优化相关问题，请联系开发团队。

# Analyze Workspace Code Task

## Purpose

Perform comprehensive analysis of all MATLAB code files in the workspace, including functionality assessment, documentation evaluation, and quality metrics calculation.

## Process

### 1. Workspace Scanning and Inventory

**File Discovery:**
- Scan workspace for all .m files (functions, scripts, classes)
- Identify data files (.mat, .txt, .csv, .xlsx, etc.)
- Catalog project structure and organization
- Create file inventory with basic metadata

**Initial Assessment:**
- Determine file types (function, script, class, app)
- Identify main entry points and workflow files
- Map file relationships and dependencies
- Assess overall project complexity

### 2. Code Functionality Analysis

**Function Analysis:**
- Extract function signatures (inputs, outputs)
- Identify function purpose and algorithm type
- Analyze computational complexity
- Assess vectorization opportunities

**Script Analysis:**
- Identify script purpose and workflow
- Map data flow and processing steps
- Identify configuration and parameter sections
- Assess modularity and organization

**Algorithm Understanding:**
- Identify mathematical algorithms and methods
- Understand data processing pipelines
- Map computational workflows
- Assess algorithm efficiency and approach

### 3. Documentation Assessment

**Comment Coverage Analysis:**
- Identify functions without header comments
- Assess quality of existing documentation
- Check for inline comment coverage
- Evaluate help text completeness

**Documentation Standards Check:**
- Verify MATLAB documentation format compliance
- Check for proper use of %% and % formatting
- Assess example usage and syntax documentation
- Evaluate parameter and return value descriptions

### 4. Code Quality Evaluation

**Readability Assessment:**
- Variable naming convention analysis
- Code structure and organization evaluation
- Indentation and formatting consistency
- Comment quality and clarity

**Maintainability Metrics:**
- Function length and complexity analysis
- Code duplication detection
- Modularity and reusability assessment
- Configuration management evaluation

**Performance Considerations:**
- Vectorization opportunities identification
- Memory usage and preallocation analysis
- Algorithm efficiency assessment
- MATLAB best practices compliance

### 5. Dependency and Relationship Mapping

**File Dependencies:**
- Map function call relationships
- Identify data file dependencies
- Create dependency graphs
- Assess coupling and cohesion

**Data Flow Analysis:**
- Trace data processing pipelines
- Identify input/output relationships
- Map data transformation steps
- Assess data validation and error handling

### 6. Issue Identification and Prioritization

**Code Issues:**
- Identify potential bugs and errors
- Flag performance bottlenecks
- Highlight maintainability concerns
- Assess security and robustness issues

**Documentation Gaps:**
- Missing function headers
- Inadequate inline comments
- Unclear parameter descriptions
- Missing usage examples

**Quality Concerns:**
- Code complexity issues
- Poor naming conventions
- Inconsistent formatting
- Lack of error handling

## Deliverables

### Analysis Summary
- Total files analyzed
- Code quality overview
- Documentation coverage statistics
- Key findings and recommendations

### Detailed File Analysis
- Per-file functionality description
- Quality metrics and scores
- Documentation status
- Improvement recommendations

### Dependency Map
- File relationship diagram
- Data flow visualization
- Critical path identification
- Modularity assessment

### Quality Report
- Code quality metrics
- Performance assessment
- Maintainability evaluation
- Standards compliance check

## Quality Criteria

**Comprehensive (5/5):**
- All files thoroughly analyzed
- Complete functionality understanding
- Detailed quality metrics
- Comprehensive documentation assessment

**Thorough (4/5):**
- Most files analyzed in detail
- Good functionality understanding
- Basic quality metrics
- Adequate documentation review

**Adequate (3/5):**
- Basic file analysis completed
- General functionality identified
- Limited quality assessment
- Minimal documentation review

**Incomplete (2/5):**
- Partial file analysis
- Unclear functionality understanding
- No quality metrics
- Poor documentation assessment

**Insufficient (1/5):**
- Minimal analysis performed
- No clear understanding
- No quality evaluation
- No documentation review

## Integration Points

This task integrates with:
- enhance-matlab-comments task
- generate-code-analysis-report task
- assess-code-quality task
- matlab-code-quality-checklist

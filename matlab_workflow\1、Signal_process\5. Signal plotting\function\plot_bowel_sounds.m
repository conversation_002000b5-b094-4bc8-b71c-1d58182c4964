%% 肠鸣音数据可视化函数
% 功能：绘制肠鸣音统计数据的专业柱状图，支持上午/下午时间段划分
% 输入：N×3数据矩阵（SB、MB、CRS列）、上午数据长度、图表位置、标题、坐标轴范围
% 输出：带有时间段分割线和专业格式的分组柱状图
% 处理步骤：
%   1. 创建分组柱状图并设置专业配色方案
%   2. 添加上午/下午分割线和时间段标注
%   3. 配置坐标轴刻度和标签格式
%   4. 设置图例、字体和整体样式
%   5. 输出调试信息显示数据统计
% 应用场景：肠鸣音研究中的数据可视化和结果展示
function plot_bowel_sounds(data, morning_length, position, title_text, x_range, y_range)
    % 绘制肠鸣音统计柱状图
    % 输入参数：
    % data: N x 3 矩阵，包含SB、MB、CRS的数据
    % morning_length: 上午数据的长度，用于划分上午和下午
    % position: 图表位置和大小 [left bottom width height]
    % title_text: 图表标题
    % x_range: x轴范围 [min max]
    % y_range: y轴范围 [min max]

    % 创建图表
    figure('Position', position);

    % 创建新的x轴位置，增加组间距
    spacing_factor = 1;  % 可以根据需要调整这个值来改变组间距
    x = 1:spacing_factor:size(data,1)*spacing_factor;  % 使用统一的间距因子

    % 绘制分组柱状图，使用新的x轴位置
    b = bar(x, data, 1, 'grouped');


    % 设置颜色和透明度
    % 设置颜色和透明度
    set(b(1), 'FaceColor', [132/255, 94/255, 194/255], 'FaceAlpha', 0.8);  % SB #845ec2
    set(b(2), 'FaceColor', [214/255, 93/255, 177/255], 'FaceAlpha', 0.8);  % MB #d65db1
    % set(b(3), 'FaceColor', [255/255, 150/255, 113/255], 'FaceAlpha', 0.8);  % CRS ##ff9671
    set(b(3), 'FaceColor', [255/255, 111/255, 145/255], 'FaceAlpha', 0.8);  % CRS #ff6f91

    % 添加分割线表示上午和下午的分界
    hold on;
    if morning_length > 0
        plot([morning_length+0.5 morning_length+0.5], y_range, '--k', 'LineWidth', 1);
        text(morning_length/2, y_range(2)*0.95, 'Morning', ...
            'HorizontalAlignment', 'center', ...
            'FontSize', 16, ...
            'FontName', 'Times New Roman', ...
            'FontWeight', 'bold');
        text(morning_length + (size(data,1)-morning_length)/2, y_range(2)*0.95, 'Afternoon', ...
            'HorizontalAlignment', 'center', ...
            'FontSize', 16, ...
            'FontName', 'Times New Roman', ...
            'FontWeight', 'bold');
    end

    % 设置坐标轴属性
    ax = gca;
    ax.FontSize = 16;
    ax.FontName = 'Times New Roman';
    ax.FontWeight = 'bold';
    ax.XLim = x_range+0.001;
    ax.YLim = y_range;

    % xlim(0, 360);
    % ylim(0, 0.1);

    % 设置x轴刻度
    time_points = [0 60 120 180 240 300 360];
    tick_positions = time_points/10 + 1;  % 转换为数据点索引
    xticks(tick_positions);
    xticklabels(time_points);

    % 设置标签
    xlabel('Time (min)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Bowel sound peak counts (counts·10min^{-1})', 'FontSize', 18, ...
        'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');


    % % 设置标签
    % xlabel('Time (min)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    % ylabel('Bowel sound intensity (dB·10min^{-1})', 'FontSize', 18, ...
    %     'FontName', 'Times New Roman', 'FontWeight', 'bold');
    % title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    

    % 添加图例
    legend({'SB', 'MB', 'CRS'}, 'Location', 'northeast', ...
        'FontSize', 16, 'FontName', 'Times New Roman', 'FontWeight', 'bold');

    % 设置网格和背景
    % grid on;
    set(gca, 'Box', 'on');
    set(gcf, 'Color', 'white');

    % 打印数据信息用于调试
    fprintf('数据大小: %d x %d\n', size(data));
    fprintf('最后几个数据点的SB值:\n');
    last_points = min(5, size(data,1));
    for i = size(data,1)-last_points+1:size(data,1)
        fprintf('时间点 %d (%.0f min): SB=%.2f, MB=%.2f, CRS=%.2f\n', ...
            i, (i-1)*10, data(i,1), data(i,2), data(i,3));
    end
end 







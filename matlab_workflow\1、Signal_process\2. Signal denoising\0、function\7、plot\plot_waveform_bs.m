function plot_waveform_bs(t, data, title_text)
%PLOT_WAVEFORM_BS 标准化波形绘制函数（带阈值参考线）
%   绘制时域信号波形，并添加幅值阈值参考线，用于信号质量评估和可视化分析。
%   该函数提供专业的科研论文级别的图形格式。
%
%   语法:
%   plot_waveform_bs(t, data, title_text)
%
%   输入参数:
%   t          - 时间向量 (数值向量，单位：秒)
%   data       - 信号数据 (数值向量，归一化幅值)
%   title_text - 图形标题 (字符串)
%
%   输出参数:
%   无 - 直接生成图形显示
%
%   图形特征:
%   - 主信号波形：蓝色实线
%   - 高阈值参考线：±0.2 (红色实线)
%   - 低阈值参考线：±0.02 (洋红色实线)
%   - X轴网格：5秒间隔
%   - Y轴范围：[-0.5, 0.5]
%   - 专业字体：Times New Roman, 加粗
%
%   阈值含义:
%   - ±0.2: 信号饱和或异常检测阈值
%   - ±0.02: 信号有效性最低阈值
%   - 网格间隔: 便于时间定位和分析
%
%   应用场景:
%   - 信号质量评估和可视化
%   - 降噪效果对比展示
%   - 科研论文图形制作
%   - 信号处理结果展示
%
%   示例:
%   % 基本用法
%   fs = 1000; % 采样率
%   t = (0:999)/fs; % 时间向量
%   signal = 0.1*sin(2*pi*50*t) + 0.05*randn(size(t)); % 测试信号
%   figure;
%   plot_waveform_bs(t, signal, '测试信号波形');
%
%   % 多子图对比
%   figure('Position', [100, 100, 1200, 400]);
%   subplot(1,2,1); plot_waveform_bs(t, original, '原始信号');
%   subplot(1,2,2); plot_waveform_bs(t, filtered, '滤波后信号');
%
%   注意事项:
%   - 输入数据应已归一化到合适范围
%   - 时间向量和数据向量长度必须相同
%   - 函数会自动设置图形格式，无需额外格式化
%   - 适用于时长5秒以上的信号显示
%
%   参见: PLOT_SPECTROGRAM_BS, PLOT_THRESHOLDED_WAVEFORM
%
%   作者: [作者姓名]
%   日期: [创建日期]
%   版本: 1.0

function plot_waveform_bs(t, data, title_text)
    plot(t, data); % 直接绘制波形
    hold on; % 保持当前图形，以便添加新的绘图元素
    
    % 添加两条红色水平线
    xrange = [min(t) max(t)]; % 获取x轴范围
    plot(xrange, [-0.2 -0.2], 'r-', 'LineWidth', 1.5); % 画-0.2水平线
    plot(xrange, [0.2 0.2], 'r-', 'LineWidth', 1.5); % 画0.2水平线
    
    % 添加两条蓝色水平线
    plot(xrange, [-0.02 -0.02], 'm-', 'LineWidth', 1.5); % 画-0.1水平线
    plot(xrange, [0.02 0.02], 'm-', 'LineWidth', 1.5); % 画0.1水平线
    
    ax = gca;
    ax.FontSize = 16; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = "bold";
    ax.YLim = [-0.5 0.5]; % 自定义纵坐标范围
    ax.XLim = [min(t) max(t)]; % 自动调整横坐标范围为时间序列的最小值和最大值
    
    % 设置网格
    ax.XGrid = 'on'; % 打开X轴网格
    ax.YGrid = 'off'; % 关闭Y轴网格
    % 设置网格间距为5
    start_x = ceil(min(t)/5)*5; % 确保起始点是5的倍数
    end_x = floor(max(t)/5)*5; % 确保结束点是5的倍数
    ax.XTick = start_x:5:end_x; % 设置X轴刻度间隔为5
    
    yticks([-1 -0.5 0 0.5 1]); % 自定义纵坐标刻度
    xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Normalized amplitude', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    
    hold off; % 关闭图形保持
end









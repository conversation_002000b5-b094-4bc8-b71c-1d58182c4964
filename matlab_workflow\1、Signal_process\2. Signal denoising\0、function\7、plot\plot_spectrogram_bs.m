function plot_spectrogram_bs(data, fs, title_text)
%PLOT_SPECTROGRAM_BS 信号频谱图绘制函数
%   计算并绘制信号的时频谱图，使用短时傅里叶变换(STFT)分析信号的频域特征。
%   该函数提供专业的科研级别频谱图可视化，适用于信号分析和降噪效果评估。
%
%   语法:
%   plot_spectrogram_bs(data, fs, title_text)
%
%   输入参数:
%   data       - 输入信号数据 (数值向量)
%   fs         - 采样频率 (标量，单位：Hz)
%   title_text - 图形标题 (字符串)
%
%   输出参数:
%   无 - 直接生成频谱图显示
%
%   技术参数:
%   - 窗口长度: 0.05秒 (50ms)
%   - 窗口重叠: 90% (45ms重叠)
%   - FFT点数: 采样率的一半
%   - 窗口类型: 汉明窗 (MATLAB默认)
%   - 颜色范围: [-90, -50] dB
%   - 颜色映射: Inferno (感知均匀)
%
%   图形特征:
%   - X轴: 时间 (秒)
%   - Y轴: 频率 (Hz)
%   - 颜色: 功率谱密度 (dB)
%   - 视角: 俯视图 (0°, 90°)
%   - 字体: Times New Roman, 加粗
%
%   应用场景:
%   - 信号频域特征分析
%   - 降噪算法效果评估
%   - 频谱污染检测
%   - 科研论文图形制作
%
%   示例:
%   % 基本用法
%   fs = 2570; % 采样率
%   t = (0:fs-1)/fs; % 1秒信号
%   signal = sin(2*pi*100*t) + 0.5*sin(2*pi*300*t); % 双频信号
%   figure;
%   plot_spectrogram_bs(signal, fs, '双频测试信号频谱图');
%
%   % 降噪前后对比
%   figure('Position', [100, 100, 1200, 400]);
%   subplot(1,2,1); plot_spectrogram_bs(noisy_signal, fs, '降噪前');
%   subplot(1,2,2); plot_spectrogram_bs(clean_signal, fs, '降噪后');
%
%   注意事项:
%   - 信号长度应足够长以获得良好的频率分辨率
%   - 采样率应满足奈奎斯特定理
%   - 颜色范围可根据信号特征调整
%   - 需要inferno颜色映射函数支持
%
%   参见: SPECTROGRAM, INFERNO, PLOT_WAVEFORM_BS
%
%   作者: [作者姓名]
%   日期: [创建日期]
%   版本: 1.0
    w = 0.05; % 窗口宽度设置为0.05秒
    win = round(fs * w); % 将窗口宽度转换为采样点数
    ov = round(fs * w * 0.9); % 设置90%的窗口重叠
    nfft = round(fs * 0.5); % 设置FFT点数为采样率的一半
    
    % 计算频谱图
    [S, F, T, P] = spectrogram(data, win, ov, nfft, fs);
    
    % 绘制频谱图
    surf(T, F, 10 * log10(P), 'edgecolor', 'none'); % 绘制功率谱密度的表面图
    axis tight;
    view(0, 90); % 设置为俯视视角
    caxis([-90 -50]); % 控制颜色范围
    colorbar;
    colormap(inferno); % 使用'inferno'颜色映射
    
    % 设置坐标轴及标题
    ax = gca;
    ax.FontSize = 16;
    ax.FontName = 'Times New Roman';
    ax.FontWeight = "bold";
    xlabel('Time (s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Freq (Hz)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
end



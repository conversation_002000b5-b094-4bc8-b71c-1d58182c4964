function plot_mvtr_bar(csv_file_path, save_figure)
% PLOT_MVTR_BAR 绘制MVTR结果的柱状图
%
% 输入参数:
%   csv_file_path - CSV文件路径
%   save_figure   - 是否保存图片 (true/false)，默认为false
%
% 功能:
%   读取CSV文件，以第一列作为横坐标标签，最后一列作为纵坐标数值
%   绘制柱状图并进行美化
%
% 示例:
%   plot_mvtr_bar('./results.csv')
%   plot_mvtr_bar('./results.csv', true)

% 设置默认参数
if nargin < 2
    save_figure = false;
end

% 检查输入文件是否存在
if ~exist(csv_file_path, 'file')
    error('CSV文件不存在: %s', csv_file_path);
end

try
    % 读取CSV文件
    fprintf('正在读取CSV文件: %s\n', csv_file_path);
    data = readtable(csv_file_path, 'Encoding', 'UTF-8');
    
    % 检查数据
    if height(data) == 0
        error('CSV文件为空');
    end
    
    % 获取第一列（材料名称）和最后一列（数值）
    materials = data{:, 1};  % 第一列
    values = data{:, end};   % 最后一列
    
    % 处理材料名称（如果是数值，转换为字符串）
    if isnumeric(materials)
        materials = cellstr(string(materials));
    elseif ~iscell(materials)
        materials = cellstr(materials);
    end
    
    % 确保数值是数值类型
    if iscell(values)
        values = cell2mat(values);
    end
    
    % 创建图形
    figure('Position', [100, 100, 800, 600]);
    
    % 绘制柱状图
    bar_handle = bar(values, 'FaceColor', [0.2, 0.6, 0.8], 'EdgeColor', [0.1, 0.3, 0.5], 'LineWidth', 1.5);
    
    % 设置横坐标标签
    set(gca, 'XTickLabel', materials);
    set(gca, 'XTick', 1:length(materials));
    
    % 旋转横坐标标签以避免重叠
    xtickangle(45);
    
    % 设置标题和标签
    title('材料MVTR测试结果', 'FontSize', 16, 'FontWeight', 'bold');
    xlabel('材料类型', 'FontSize', 14);
    
    % 根据数值范围判断可能的单位
    if max(values) > 1
        ylabel('MVTR (g/(m²·24h))', 'FontSize', 14);
    else
        ylabel('MVTR (kg/(m²·24h))', 'FontSize', 14);
    end
    
    % 设置网格
    grid on;
    grid minor;
    set(gca, 'GridAlpha', 0.3, 'MinorGridAlpha', 0.1);
    
    % 美化图形
    set(gca, 'FontSize', 12);
    set(gca, 'Box', 'on');
    
    % 在每个柱子上显示数值
    for i = 1:length(values)
        if values(i) > 1
            text(i, values(i) + max(values)*0.01, sprintf('%.1f', values(i)), ...
                'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
                'FontSize', 10, 'FontWeight', 'bold');
        else
            text(i, values(i) + max(values)*0.01, sprintf('%.3f', values(i)), ...
                'HorizontalAlignment', 'center', 'VerticalAlignment', 'bottom', ...
                'FontSize', 10, 'FontWeight', 'bold');
        end
    end
    
    % 调整Y轴范围以容纳数值标签
    ylim([0, max(values) * 1.15]);
    
    % 调整边距
    set(gca, 'Position', [0.15, 0.15, 0.75, 0.75]);
    
    % 添加颜色渐变效果
    colormap(cool);
    if length(values) > 1
        % 根据数值大小设置颜色
        normalized_values = (values - min(values)) / (max(values) - min(values));
        colors = cool(256);
        for i = 1:length(values)
            color_idx = max(1, min(256, round(normalized_values(i) * 255 + 1)));
            bar_handle.CData(i, :) = colors(color_idx, :);
        end
    end
    
    % 显示统计信息
    fprintf('\n图形统计信息:\n');
    fprintf('  样品数量: %d\n', length(values));
    fprintf('  最大值: %.6f\n', max(values));
    fprintf('  最小值: %.6f\n', min(values));
    fprintf('  平均值: %.6f\n', mean(values));
    fprintf('  标准差: %.6f\n', std(values));
    
    % 保存图片
    if save_figure
        [filepath, name, ~] = fileparts(csv_file_path);
        timestamp = datestr(now, 'yyyymmdd_HHMMSS');
        
        % 保存为PNG格式
        png_filename = sprintf('MVTR_bar_chart_%s_%s.png', name, timestamp);
        saveas(gcf, png_filename, 'png');
        fprintf('\n图片已保存为: %s\n', png_filename);
        
        % 保存为高质量PDF格式
        pdf_filename = sprintf('MVTR_bar_chart_%s_%s.pdf', name, timestamp);
        saveas(gcf, pdf_filename, 'pdf');
        fprintf('图片已保存为: %s\n', pdf_filename);
        
        % 保存为MATLAB图形文件
        fig_filename = sprintf('MVTR_bar_chart_%s_%s.fig', name, timestamp);
        saveas(gcf, fig_filename, 'fig');
        fprintf('图形文件已保存为: %s\n', fig_filename);
    end
    
    fprintf('\n柱状图绘制完成！\n');
    
catch ME
    fprintf('错误: %s\n', ME.message);
    rethrow(ME);
end

end

function plot_waveform_with_envelope(t, signal, envelope, peak_times, peaks, position, rows, cols, index, title_text)
    figure('Position', position); % 设置图框位置和大小，[left, bottom, width, height]
    subplot(rows, cols, index);
    plot(t, real(signal), 'k');
    hold on;
    plot(t, envelope, 'g');
    plot(peak_times, peaks, 'o', 'MarkerEdgeColor', 'r', 'MarkerSize', 5);
    hold off;
    
    ax = gca;
    ax.FontSize = 16; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = "bold";
    ax.YLim = [-1 1];
    ax.XLim = [0 1500];
    yticks([-1 -0.5 0 0.5 1]); % 自定义纵坐标刻度
    
    xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Normalized amplitude', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    
    legend('Signal', 'Envelope', 'Peaks', 'FontSize', 14, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    grid on;
end
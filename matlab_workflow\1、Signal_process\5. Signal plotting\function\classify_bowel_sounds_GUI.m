function classify_bowel_sounds_GUI(signal, fs, peak_indices, peak_values, envelope)
    % 创建一个新的图形窗口
    fig = figure('Name', '肠鸣音分类', 'Position', [100, 100, 1200, 800]);

    % 绘制信号、包络和检测到的峰值
    t = (0:length(signal)-1)' / fs;  % 确保 t 为列向量
    ax = axes('Parent', fig, 'Position', [0.05, 0.45, 0.9, 0.5]);  % 调整位置和大小
    plot(ax, t, signal, 'k');
    hold on;
    plot(ax, t, envelope, 'g');
    plot(ax, t(peak_indices), peak_values, 'ro', 'MarkerSize', 5);
    hold off;
    xlabel(ax, '时间 (秒)');
    ylabel(ax, '幅值');
    title(ax, '信号、包络和检测到的峰值');
    legend(ax, '信号', '包络', '峰值');
    grid(ax, 'on');

    % 启用交互式缩放和平移
    zoom(ax, 'on');
    pan(ax, 'on');

    % 定义肠鸣音类型的名称
    types = {'SB', 'MB', 'CRS'};  % 修改类型名称
    num_types = length(types);

    % 在 GUI 中添加三个面板，每个面板对应一种肠鸣音类型，横向排列
    panel_width = 0.28;  % 每个面板的宽度
    panel_height = 0.35;  % 面板的高度
    panel_spacing = 0.03;  % 面板之间的水平间距

    for i = 1:num_types
        % 计算面板的位置
        panel_position = [0.05 + (i-1)*(panel_width + panel_spacing), 0.05, panel_width, panel_height];

        % 创建面板
        panel = uipanel('Parent', fig, 'Title', types{i}, 'FontSize', 12, 'Position', panel_position);

        % 获取面板的像素尺寸
        panel_units = get(panel, 'Units');
        set(panel, 'Units', 'pixels');
        panel_pos = get(panel, 'Position');
        panel_width_px = panel_pos(3);
        panel_height_px = panel_pos(4);

        % 控件的尺寸和间距
        label_width = 120;
        label_height = 30;
        edit_width = 150;
        edit_height = 30;
        button_width = 120;
        button_height = 40;
        vertical_spacing = 30;

        % 计算总控件高度
        total_height = 2*(label_height + vertical_spacing) + button_height;

        % 计算控件起始的 y 坐标，使其在面板中垂直居中
        start_y = (panel_height_px - total_height) / 2;

        % 计算控件的 x 坐标，使其在面板中水平居中
        label_x = (panel_width_px - (label_width + edit_width + 20)) / 2;
        edit_x = label_x + label_width + 20;

        % 添加开始时间的文本框和标签
        uicontrol('Parent', panel, 'Style', 'text', 'Position', [label_x, start_y + label_height + vertical_spacing + button_height + vertical_spacing, label_width, label_height], 'String', '开始时间：', 'FontSize', 12, 'HorizontalAlignment', 'right');
        start_time_edit = uicontrol('Parent', panel, 'Style', 'edit', 'Position', [edit_x, start_y + label_height + vertical_spacing + button_height + vertical_spacing, edit_width, edit_height], 'FontSize', 12);

        % 添加结束时间的文本框和标签
        uicontrol('Parent', panel, 'Style', 'text', 'Position', [label_x, start_y + button_height + vertical_spacing, label_width, label_height], 'String', '结束时间：', 'FontSize', 12, 'HorizontalAlignment', 'right');
        end_time_edit = uicontrol('Parent', panel, 'Style', 'edit', 'Position', [edit_x, start_y + button_height + vertical_spacing, edit_width, edit_height], 'FontSize', 12);

        % 添加提交按钮
        button_x = (panel_width_px - button_width) / 2;
        submit_button = uicontrol('Parent', panel, 'Style', 'pushbutton', 'Position', [button_x, start_y, button_width, button_height], 'String', '提交', 'FontSize', 12);

        % 恢复面板的单位
        set(panel, 'Units', panel_units);

        % 为每个按钮添加回调函数
        set(submit_button, 'Callback', @(src, event)submitCallback(start_time_edit, end_time_edit, signal, fs, types{i}));
    end

    % 嵌套的回调函数
    function submitCallback(start_edit, end_edit, signal, fs, type_name)
        % 获取用户输入的开始和结束时间
        start_time_str = get(start_edit, 'String');
        end_time_str = get(end_edit, 'String');

        % 检查输入是否为空
        if isempty(start_time_str) || isempty(end_time_str)
            errordlg('请输入开始时间和结束时间。', '输入错误');
            return;
        end

        % 将输入的字符串转换为数字
        start_time = str2double(start_time_str);
        end_time = str2double(end_time_str);

        % 检查转换是否成功
        if isnan(start_time) || isnan(end_time)
            errordlg('请输入有效的数字。', '输入错误');
            return;
        end

        % 检查时间的合理性
        if start_time < 0 || end_time > t(end) || start_time >= end_time
            errordlg('请输入合理的时间范围。', '输入错误');
            return;
        end

        % 提取对应时间段的信号
        start_index = max(1, floor(start_time * fs));
        end_index = min(length(signal), ceil(end_time * fs));
        selected_signal = signal(start_index:end_index);
        selected_time = t(start_index:end_index);

        % 创建 BS 文件夹（如果不存在）
        if ~exist('BS', 'dir')
            mkdir('BS');
        end

        % 生成唯一的文件名，包含类型名称和时间戳
        timestamp = datestr(now, 'yyyymmdd_HHMMSSFFF');
        filename = sprintf('BS/bowel_sound_%s_%s.csv', type_name, timestamp);

        % 保存数据到 CSV 文件
        csvwrite(filename, [selected_time, selected_signal]);

        % 提示用户保存成功
        msgbox(sprintf('已保存 %s 的数据至 %s。', type_name, filename), '保存成功');

        % 清空输入框
        set(start_edit, 'String', '');
        set(end_edit, 'String', '');
    end
end

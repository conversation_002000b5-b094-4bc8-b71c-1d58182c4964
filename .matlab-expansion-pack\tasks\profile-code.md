# profile-code

Run MATLAB profiler and provide comprehensive performance analysis with optimization recommendations.

## Purpose
Analyze MATLAB code performance, identify bottlenecks, and provide specific optimization recommendations.

## Inputs
- **targetFunction** (required): Function or script to profile
- **inputData** (optional): Test data for profiling
- **iterations** (optional): Number of profiling iterations (default: 10)
- **memoryProfiling** (optional): Enable memory profiling (default: true)

## Process

### 1. Profiling Setup
```matlab
% Clear previous profiling data
profile clear

% Configure profiling options
profile on -detail builtin -memory
```

### 2. Performance Measurement
- Execute target function multiple iterations
- Measure execution time and memory usage
- Capture detailed function call hierarchy

### 3. Bottleneck Analysis
```matlab
% Get profiling results
profileInfo = profile('info');

% Analyze function call times
functionStats = profileInfo.FunctionTable;
[~, slowestIdx] = sort([functionStats.TotalTime], 'descend');
```

### 4. Vectorization Analysis
- Identify loops that can be vectorized
- Detect inefficient matrix operations
- Suggest MATLAB built-in function alternatives

### 5. Memory Usage Analysis
- Track memory allocation patterns
- Identify memory leaks or excessive usage
- Suggest memory optimization strategies

### 6. Optimization Recommendations
- **Vectorization**: Replace loops with vector operations
- **Preallocation**: Suggest array preallocation
- **Built-in Functions**: Recommend optimized MATLAB functions
- **Algorithm Improvements**: Suggest algorithmic optimizations

## Outputs
- **executionTime**: Total and per-function execution times
- **memoryUsage**: Peak and average memory consumption
- **bottlenecks**: Top performance bottlenecks identified
- **optimizationSuggestions**: Specific code improvement recommendations
- **vectorizationOpportunities**: Loops that can be vectorized

## Performance Metrics
- **Execution Time**: Total runtime and function breakdown
- **Memory Usage**: Peak memory and allocation patterns
- **Vectorization Ratio**: Percentage of vectorized operations
- **Function Call Overhead**: Time spent in function calls vs computation

## Optimization Categories

### High Impact
- Replace loops with vectorized operations
- Use built-in functions instead of custom implementations
- Preallocate arrays to avoid dynamic resizing

### Medium Impact
- Optimize matrix operations order
- Reduce function call overhead
- Improve memory access patterns

### Low Impact
- Code style improvements
- Minor algorithmic tweaks
- Documentation and readability

## Success Criteria
- Profiling completed successfully
- Performance bottlenecks identified
- Actionable optimization recommendations provided
- Performance metrics documented

## Error Handling
- Function execution failure: Report error and partial results
- Memory profiling unavailable: Continue with time profiling only
- Large data sets: Use sampling for profiling

## Integration
- Updates matlabPerformanceLog with profiling results
- Integrates with optimization workflow
- Supports both interactive and automated profiling

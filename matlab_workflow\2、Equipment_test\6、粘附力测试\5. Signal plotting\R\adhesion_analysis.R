# =============================================================================
# 粘附力测试数据分析主程序
#
# 功能：完全替代MATLAB的粘附力测试数据分析和可视化
# 对应MATLAB的adhesion.m主程序
#
# 依赖文件：
# - calculatePeelingEnergy.R: 剥离能量计算函数
# - plotFunctions.R: 绘图函数
# - dataProcessing.R: 数据处理函数
#
# 输入：gel.csv, no_gel.csv (CSV数据文件)
# 输出：
# - 处理后的数据（内存中）
# - 力-位移曲线图（ggplot对象）
# - 剥离能量柱状图（ggplot对象）
# - 分析结果摘要（data.frame）
#
# 使用方法：
# source("adhesion_analysis.R")
# 结果保存在 results_gel 和 results_no_gel 变量中
# =============================================================================

# 清理工作环境
rm(list = ls())

# 检查并安装必要的包
required_packages <- c("ggplot2", "dplyr")

for (pkg in required_packages) {
  if (!require(pkg, character.only = TRUE, quietly = TRUE)) {
    install.packages(pkg)
    library(pkg, character.only = TRUE)
  }
}

# 加载自定义函数
source("calculatePeelingEnergy.R")
source("plotCurve.R")
source("plotCurveWithCI.R")  # 新增：带置信区间的绘图函数
source("plotBarChart.R")
source("plotBoxPlot.R")      # 新增：箱线图绘制函数
source("plotViolinPlot.R")   # 新增：小提琴图绘制函数
source("dataProcessing.R")

# =============================================================================
# 主分析流程
# =============================================================================

main_analysis <- function(data_file = "gel.csv") {

  # 步骤1: 读取和预处理数据
  processed_data <- readAndProcessData(data_file)
  checkDataQuality(processed_data)
  
  # 提取处理后的数据
  force1 <- processed_data$force1
  displacement1 <- processed_data$displacement1
  force2 <- processed_data$force2
  displacement2 <- processed_data$displacement2
  force3 <- processed_data$force3
  displacement3 <- processed_data$displacement3

  # 步骤2: 生成力-位移曲线图
  # 根据文件名设置标题
  title_text <- if (grepl("gel\\.csv", data_file)) {
    "2733+Silbione"
  } else if (grepl("no_gel\\.csv", data_file)) {
    "2733"
  } else {
    basename(data_file)  # 使用文件名作为默认标题
  }

  displacement_force_plot <- plotDisplacementForce(
    displacement1, force1,
    displacement2, force2,
    displacement3, force3,
    title_text = title_text
  )

  # 步骤2.5: 生成带置信区间的力-位移曲线图
  displacement_force_plot_with_ci <- plotDisplacementForceWithCI(
    displacement1, force1,
    displacement2, force2,
    displacement3, force3,
    title_text = paste(title_text, "- With Confidence Interval"),
    show_ci = TRUE,
    ci_level = 0.95,
    smooth_method = "loess",
    span_value = 0.15  # 使用更小的span值来产生更宽的置信区间
  )

  # 步骤3: 计算剥离能量
  peelingEnergy1 <- calculatePeelingEnergy(force1, displacement1)
  peelingEnergy2 <- calculatePeelingEnergy(force2, displacement2)
  peelingEnergy3 <- calculatePeelingEnergy(force3, displacement3)
  peelingEnergies <- c(peelingEnergy1, peelingEnergy2, peelingEnergy3)
  
  # 步骤4: 生成剥离能量柱状图
  # 使用实际计算的剥离能量数据，而不是硬编码数值
  # 创建单个数据集的柱状图，显示三次测量的结果
  peeling_energy_plot <- plotPeelingEnergy(
    group1_data = peelingEnergies,  # 使用实际计算的数据
    group2_data = NULL,             # 单个数据文件分析时不需要第二组
    group1_label = title_text,      # 使用动态标题
    group2_label = NULL
  )

  # 步骤4.5: 生成剥离能量箱线图
  # 箱线图能更好地显示数据分布、中位数、四分位数等统计信息
  peeling_energy_boxplot <- plotPeelingEnergyBoxPlot(
    group1_data = peelingEnergies,  # 使用实际计算的数据
    group2_data = NULL,             # 单个数据文件分析时不需要第二组
    group1_label = title_text,      # 使用动态标题
    group2_label = NULL,
    show_points = TRUE,             # 显示原始数据点
    show_mean = TRUE                # 显示均值点
  )

  # 步骤4.6: 生成剥离能量小提琴图
  # 小提琴图结合了箱线图和核密度估计，能够显示数据的分布形状和密度
  peeling_energy_violinplot <- plotPeelingEnergyViolinPlot(
    group1_data = peelingEnergies,  # 使用实际计算的数据
    group2_data = NULL,             # 单个数据文件分析时不需要第二组
    group1_label = title_text,      # 使用动态标题
    group2_label = NULL,
    show_points = TRUE,             # 显示原始数据点
    show_mean = TRUE,               # 显示均值点
    show_median = TRUE,             # 显示中位数线
    show_quartiles = FALSE,         # 不显示四分位数线（避免过于复杂）
    violin_width = 0.8,             # 小提琴图宽度
    trim_tails = TRUE               # 修剪尾部
  )

  # 步骤5: 准备分析结果
  results_summary <- data.frame(
    Group = c("Group 1", "Group 2", "Group 3", "Mean", "Std Dev"),
    PeelingEnergy_Jm2 = c(peelingEnergies, mean(peelingEnergies), sd(peelingEnergies)),
    DataPoints = c(processed_data$valid_counts, NA, NA)
  )
  
  # 返回结果
  return(list(
    processed_data = processed_data,
    peeling_energies = peelingEnergies,
    displacement_force_plot = displacement_force_plot,
    displacement_force_plot_with_ci = displacement_force_plot_with_ci,  # 新增：带置信区间的图表
    peeling_energy_plot = peeling_energy_plot,
    peeling_energy_boxplot = peeling_energy_boxplot,  # 新增：箱线图
    peeling_energy_violinplot = peeling_energy_violinplot,  # 新增：小提琴图
    summary = results_summary
  ))
}

# =============================================================================
# 执行分析
# =============================================================================

# 分析gel.csv数据
results_gel <- main_analysis("gel.csv")

# 分析no_gel.csv数据
results_no_gel <- main_analysis("no_gel.csv")

# 显示gel.csv的图表
print("=== gel.csv 数据分析结果 ===")
print("常规力-位移曲线图：")
print(results_gel$displacement_force_plot)
print("带置信区间的力-位移曲线图：")
print(results_gel$displacement_force_plot_with_ci)
print("剥离能量箱线图：")
print(results_gel$peeling_energy_boxplot)
print("剥离能量小提琴图：")
print(results_gel$peeling_energy_violinplot)

# 显示no_gel.csv的图表
print("=== no_gel.csv 数据分析结果 ===")
print("常规力-位移曲线图：")
print(results_no_gel$displacement_force_plot)
print("带置信区间的力-位移曲线图：")
print(results_no_gel$displacement_force_plot_with_ci)
print("剥离能量箱线图：")
print(results_no_gel$peeling_energy_boxplot)
print("剥离能量小提琴图：")
print(results_no_gel$peeling_energy_violinplot)

# =============================================================================
# 比较分析：生成两组数据的对比图和摘要
# =============================================================================

# 创建比较柱状图，显示两组数据的均值对比
comparison_plot <- plotPeelingEnergy(
  group1_data = results_gel$peeling_energies,
  group2_data = results_no_gel$peeling_energies,
  group1_label = "2733+Silbione",
  group2_label = "2733"
)

# 创建比较箱线图，显示两组数据的完整分布对比
comparison_boxplot <- plotPeelingEnergyBoxPlot(
  group1_data = results_gel$peeling_energies,
  group2_data = results_no_gel$peeling_energies,
  group1_label = "2733+Silbione",
  group2_label = "2733",
  show_points = TRUE,
  show_mean = TRUE
)

# 创建比较小提琴图，显示两组数据的分布形状和密度对比
comparison_violinplot <- plotPeelingEnergyViolinPlot(
  group1_data = results_gel$peeling_energies,
  group2_data = results_no_gel$peeling_energies,
  group1_label = "2733+Silbione",
  group2_label = "2733",
  show_points = TRUE,
  show_mean = TRUE,
  show_median = TRUE,
  show_quartiles = FALSE,
  violin_width = 0.8,
  trim_tails = TRUE
)

# 显示比较图
print("=== 两组数据对比分析 ===")
print("柱状图比较：")
print(comparison_plot)
print("箱线图比较：")
print(comparison_boxplot)
print("小提琴图比较：")
print(comparison_violinplot)

# 创建比较结果摘要
comparison_summary <- data.frame(
  Dataset = c("2733+Silbione (gel.csv)", "2733 (no_gel.csv)"),
  Mean_PeelingEnergy = c(mean(results_gel$peeling_energies),
                        mean(results_no_gel$peeling_energies)),
  StdDev_PeelingEnergy = c(sd(results_gel$peeling_energies),
                          sd(results_no_gel$peeling_energies)),
  Test1 = c(results_gel$peeling_energies[1], results_no_gel$peeling_energies[1]),
  Test2 = c(results_gel$peeling_energies[2], results_no_gel$peeling_energies[2]),
  Test3 = c(results_gel$peeling_energies[3], results_no_gel$peeling_energies[3])
)

# 显示比较结果
print("=== 数据比较摘要 ===")
print(comparison_summary)

# {{analysis_title}} - Data Analysis Report

[[LLM: This template guides the creation of comprehensive data analysis reports. Focus on clear methodology, rigorous analysis, and actionable insights. Present numbered options for different analysis approaches.]]

## Executive Summary

[[LLM: Start with a high-level summary that can be understood by non-technical stakeholders.]]

**Analysis Title:** {{analysis_title}}
**Analyst:** {{analyst_name}}
**Analysis Date:** {{analysis_date}}
**Report Version:** {{report_version}}

### Key Findings

[[LLM: Summarize the most important findings and insights from the analysis.]]

{{key_findings}}

### Recommendations

[[LLM: Provide clear, actionable recommendations based on the analysis results.]]

{{recommendations}}

### Business Impact

{{business_impact}}

## Analysis Overview

[[LLM: Provide context and background for the analysis.]]

### Objectives

[[LLM: Present numbered options for different analysis objectives:
1. Exploratory data analysis and pattern discovery
2. Hypothesis testing and statistical inference
3. Predictive modeling and forecasting
4. Performance monitoring and optimization
5. Comparative analysis and benchmarking
6. Root cause analysis and problem solving]]

**Primary Objectives:**
{{primary_objectives}}

**Research Questions:**
{{research_questions}}

**Success Criteria:**
{{success_criteria}}

### Scope and Limitations

**Analysis Scope:**
{{analysis_scope}}

**Data Limitations:**
{{data_limitations}}

**Methodological Constraints:**
{{methodological_constraints}}

**Assumptions:**
{{analysis_assumptions}}

## Data Description

[[LLM: Provide comprehensive description of the data used in the analysis.]]

### Data Sources

**Primary Data Sources:**
{{primary_data_sources}}

**Secondary Data Sources:**
{{secondary_data_sources}}

**Data Collection Period:**
{{data_collection_period}}

**Data Volume:**
{{data_volume}}

### Data Quality Assessment

**Completeness:**
{{data_completeness}}

**Accuracy:**
{{data_accuracy}}

**Consistency:**
{{data_consistency}}

**Timeliness:**
{{data_timeliness}}

### Data Preprocessing

**Cleaning Procedures:**
{{data_cleaning}}

**Missing Value Treatment:**
{{missing_value_treatment}}

**Outlier Detection and Treatment:**
{{outlier_treatment}}

**Data Transformations:**
{{data_transformations}}

## Methodology

[[LLM: Describe the analytical methodology and approach used.]]

### Analytical Approach

[[LLM: Based on the analysis type, describe the specific methodology used.]]

**Statistical Methods:**
{{statistical_methods}}

**Analysis Techniques:**
{{analysis_techniques}}

**Software and Tools:**
{{software_tools}}

**Validation Approach:**
{{validation_approach}}

### Experimental Design

^^CONDITION: analysis_type == "experimental"^^
**Experimental Setup:**
{{experimental_setup}}

**Control Variables:**
{{control_variables}}

**Treatment Variables:**
{{treatment_variables}}

**Randomization:**
{{randomization}}
^^/CONDITION: analysis_type^^

^^CONDITION: analysis_type == "observational"^^
**Study Design:**
{{study_design}}

**Sampling Strategy:**
{{sampling_strategy}}

**Confounding Variables:**
{{confounding_variables}}

**Bias Mitigation:**
{{bias_mitigation}}
^^/CONDITION: analysis_type^^

## Results

[[LLM: Present the analysis results in a clear, structured manner.]]

### Descriptive Statistics

**Summary Statistics:**
{{summary_statistics}}

**Distribution Analysis:**
{{distribution_analysis}}

**Correlation Analysis:**
{{correlation_analysis}}

**Trend Analysis:**
{{trend_analysis}}

### Statistical Analysis

[[LLM: Present statistical test results and their interpretation.]]

<<REPEAT section="statistical_test" count="{{test_count}}">>
**Test {{test_number}}:** {{test_name}}
- **Hypothesis:** {{test_hypothesis}}
- **Test Statistic:** {{test_statistic}}
- **P-value:** {{p_value}}
- **Confidence Interval:** {{confidence_interval}}
- **Interpretation:** {{test_interpretation}}
<</REPEAT>>

### Model Results

^^CONDITION: analysis_includes_modeling == "yes"^^
**Model Type:** {{model_type}}
**Model Performance:**
{{model_performance}}

**Model Validation:**
{{model_validation}}

**Feature Importance:**
{{feature_importance}}

**Model Interpretation:**
{{model_interpretation}}
^^/CONDITION: analysis_includes_modeling^^

## Visualizations

[[LLM: Include key visualizations that support the analysis findings.]]

### Exploratory Visualizations

**Data Distribution Plots:**
{{distribution_plots}}

**Relationship Plots:**
{{relationship_plots}}

**Trend Visualizations:**
{{trend_visualizations}}

**Comparative Visualizations:**
{{comparative_visualizations}}

### Results Visualizations

**Statistical Results:**
{{statistical_visualizations}}

**Model Results:**
{{model_visualizations}}

**Performance Metrics:**
{{performance_visualizations}}

**Prediction Visualizations:**
{{prediction_visualizations}}

## Discussion

[[LLM: Provide interpretation and context for the results.]]

### Interpretation of Results

**Key Findings Interpretation:**
{{findings_interpretation}}

**Statistical Significance:**
{{statistical_significance}}

**Practical Significance:**
{{practical_significance}}

**Unexpected Results:**
{{unexpected_results}}

### Comparison with Literature

**Previous Studies:**
{{literature_comparison}}

**Industry Benchmarks:**
{{benchmark_comparison}}

**Best Practices:**
{{best_practices_comparison}}

### Limitations and Caveats

**Analytical Limitations:**
{{analytical_limitations}}

**Data Limitations:**
{{data_limitations_discussion}}

**Generalizability:**
{{generalizability}}

**Uncertainty Quantification:**
{{uncertainty_quantification}}

## Conclusions

[[LLM: Summarize the main conclusions and their implications.]]

### Main Conclusions

{{main_conclusions}}

### Implications

**Business Implications:**
{{business_implications}}

**Technical Implications:**
{{technical_implications}}

**Strategic Implications:**
{{strategic_implications}}

### Future Research

**Recommended Follow-up Studies:**
{{future_research}}

**Data Collection Improvements:**
{{data_collection_improvements}}

**Methodological Enhancements:**
{{methodological_enhancements}}

## Recommendations

[[LLM: Provide specific, actionable recommendations based on the analysis.]]

### Immediate Actions

<<REPEAT section="immediate_action" count="{{immediate_action_count}}">>
**Action {{action_number}}:** {{action_title}}
- **Description:** {{action_description}}
- **Priority:** {{action_priority}}
- **Timeline:** {{action_timeline}}
- **Resources Required:** {{action_resources}}
- **Expected Impact:** {{action_impact}}
<</REPEAT>>

### Long-term Strategies

{{longterm_strategies}}

### Monitoring and Evaluation

**Key Performance Indicators:**
{{monitoring_kpis}}

**Monitoring Frequency:**
{{monitoring_frequency}}

**Evaluation Criteria:**
{{evaluation_criteria}}

**Review Schedule:**
{{review_schedule}}

## Appendices

[[LLM: Include supporting materials and detailed technical information.]]

### Appendix A: Technical Details

**Detailed Methodology:**
{{detailed_methodology}}

**Statistical Formulas:**
{{statistical_formulas}}

**Algorithm Descriptions:**
{{algorithm_descriptions}}

### Appendix B: Additional Results

**Supplementary Tables:**
{{supplementary_tables}}

**Additional Visualizations:**
{{additional_visualizations}}

**Sensitivity Analysis:**
{{sensitivity_analysis}}

### Appendix C: Code and Reproducibility

**MATLAB Code:**
{{matlab_code}}

**Data Processing Scripts:**
{{data_processing_scripts}}

**Reproducibility Instructions:**
{{reproducibility_instructions}}

## Quality Assurance

[[LLM: Document quality assurance measures and validation.]]

**Peer Review:** {{peer_review}}
**Data Validation:** {{data_validation}}
**Code Review:** {{code_review}}
**Results Verification:** {{results_verification}}

---

[[LLM: After completing the report, suggest next steps:
1. Review findings with stakeholders
2. Implement recommended actions
3. Set up monitoring and evaluation systems
4. Plan follow-up analyses
5. Document lessons learned and best practices]]

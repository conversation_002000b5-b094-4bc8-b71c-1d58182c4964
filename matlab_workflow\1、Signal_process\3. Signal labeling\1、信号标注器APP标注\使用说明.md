# 肠鸣音标注器使用说明

## 概述
肠鸣音标注器是一个MATLAB GUI应用程序，用于对音频/信号文件进行肠鸣音标注并自动重命名文件。

## 新增功能（v2.0）
- **文件夹记忆功能**：自动记住用户上次使用的文件夹路径
- **默认文件夹设置**：允许用户手动设置常用的工作文件夹
- **路径自动保存**：选择文件时自动保存文件夹路径
- **智能路径验证**：启动时验证保存的路径是否仍然存在

## 界面说明

### 主要控件
1. **设置默认文件夹**：手动选择并保存常用的工作文件夹
2. **清除默认文件夹**：清除已保存的默认文件夹设置
3. **当前默认文件夹显示**：显示当前设置的默认文件夹路径
4. **选择单个文件**：选择单个音频/信号文件进行标注
5. **选择多个文件**：批量选择多个文件进行标注
6. **开始标注**：开始标注流程

### 支持的文件格式
- MATLAB文件 (*.mat)
- WAV音频文件 (*.wav)
- MP3音频文件 (*.mp3)
- M4A音频文件 (*.m4a)
- FLAC音频文件 (*.flac)

## 使用步骤

### 1. 启动应用程序
```matlab
BowelSoundLabeler()
```

### 2. 设置默认文件夹（可选）
- 点击"设置默认文件夹"按钮
- 选择常用的工作文件夹
- 系统会自动保存此路径

### 3. 选择文件
- 点击"选择单个文件"或"选择多个文件"
- 文件选择对话框会自动定位到默认文件夹
- 选择完成后，系统会自动更新默认文件夹路径

### 4. 开始标注
- 点击"开始标注"按钮
- 对每个文件回答是否包含肠鸣音
- 如果包含肠鸣音，输入肠鸣音数量
- 系统会自动重命名文件

## 文件重命名规则
- **无肠鸣音**：原文件名 + "_no" + 扩展名
  - 例：`data1_5min_seg001_tt.mat` → `data1_5min_seg001_tt_no.mat`
- **有肠鸣音**：原文件名 + "_yes_N" + 扩展名（N为肠鸣音数量）
  - 例：`data1_5min_seg001_tt.mat` → `data1_5min_seg001_tt_yes_3.mat`

## 文件夹记忆功能详解

### 自动保存机制
- 每次选择文件时，系统会自动保存选择的文件夹路径
- 使用MATLAB的偏好设置系统（`setpref`/`getpref`）存储路径
- 路径信息持久保存，重启MATLAB后仍然有效

### 路径验证
- 启动时自动验证保存的路径是否存在
- 如果路径不存在，自动清除无效设置
- 确保用户始终获得有效的默认路径

### 路径显示
- 实时显示当前的默认文件夹路径
- 长路径自动截断显示（保留末尾60个字符）
- 不同状态使用不同颜色标识：
  - 绿色：已设置有效路径
  - 灰色：未设置路径
  - 红色：获取路径失败

## 错误处理
- 完善的异常处理机制
- 文件冲突检测和处理
- 输入验证（确保肠鸣音数量为有效整数）
- 详细的错误信息提示

## 操作提示
1. 首次使用建议先设置默认文件夹
2. 支持跳过不需要标注的文件
3. 文件重命名前会检查是否存在同名文件
4. 标注完成后会显示详细的处理统计信息
5. 可以随时清除默认文件夹设置

## 技术特性
- 使用现代MATLAB GUI组件（uifigure, uigridlayout）
- 响应式界面设计
- 持久化配置存储
- 智能路径管理
- 批量文件处理支持

## 故障排除
- 如果默认文件夹显示异常，可以点击"清除默认文件夹"重置
- 如果文件选择对话框无法打开，检查MATLAB版本是否支持uigetfile函数
- 如果偏好设置保存失败，检查MATLAB是否有写入权限

## 版本历史
- v1.0：基础标注功能
- v2.0：添加文件夹记忆功能，改进用户体验

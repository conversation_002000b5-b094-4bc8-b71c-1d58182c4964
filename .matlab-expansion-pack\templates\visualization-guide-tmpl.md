# {{project_name}} - Visualization Guide

[[LLM: This template guides the creation of comprehensive visualization guides for MATLAB projects. Focus on design principles, implementation guidelines, and best practices. Present numbered options for different visualization approaches.]]

## Guide Overview

[[LLM: Begin by understanding the visualization needs and context for the project.]]

**Project Name:** {{project_name}}
**Visualization Scope:** {{visualization_scope}}
**Target Audience:** {{target_audience}}
**Guide Author:** {{guide_author}}
**Creation Date:** {{creation_date}}

### Purpose and Objectives

[[LLM: Define the purpose of the visualization guide and what it aims to achieve.]]

**Guide Purpose:**
{{guide_purpose}}

**Visualization Objectives:**
{{visualization_objectives}}

**Success Criteria:**
{{success_criteria}}

## Design Principles

[[LLM: Establish the core design principles that will guide all visualization decisions.]]

### Visual Design Philosophy

[[LLM: Present numbered options for different design philosophies:
1. Minimalist and clean design
2. Rich and detailed presentations
3. Interactive and engaging visualizations
4. Traditional academic style
5. Modern infographic style
6. Technical documentation style]]

**Selected Design Philosophy:** {{design_philosophy}}
**Rationale:** {{design_rationale}}

### Core Design Principles

**Clarity and Readability:**
{{clarity_principles}}

**Data Integrity:**
{{data_integrity_principles}}

**Aesthetic Appeal:**
{{aesthetic_principles}}

**Accessibility:**
{{accessibility_principles}}

## Visual Style Guidelines

[[LLM: Define specific style guidelines for consistent visual presentation.]]

### Color Palette

**Primary Colors:**
{{primary_colors}}

**Secondary Colors:**
{{secondary_colors}}

**Accent Colors:**
{{accent_colors}}

**Accessibility Considerations:**
{{color_accessibility}}

### Typography

**Primary Font:** {{primary_font}}
**Secondary Font:** {{secondary_font}}
**Font Sizes:**
- Title: {{title_font_size}}
- Axis Labels: {{axis_label_font_size}}
- Legends: {{legend_font_size}}
- Annotations: {{annotation_font_size}}

### Layout and Spacing

**Figure Dimensions:**
{{figure_dimensions}}

**Margin Specifications:**
{{margin_specifications}}

**Subplot Spacing:**
{{subplot_spacing}}

**Element Positioning:**
{{element_positioning}}

## Visualization Types and Standards

[[LLM: Define standards for different types of visualizations used in the project.]]

### Statistical Visualizations

<<REPEAT section="stat_viz" count="{{stat_viz_count}}">>
**{{stat_viz_type}}:**
- **Use Cases:** {{stat_viz_use_cases}}
- **MATLAB Functions:** {{stat_viz_functions}}
- **Styling Guidelines:** {{stat_viz_styling}}
- **Best Practices:** {{stat_viz_best_practices}}
<</REPEAT>>

### Scientific Plots

<<REPEAT section="sci_plot" count="{{sci_plot_count}}">>
**{{sci_plot_type}}:**
- **Applications:** {{sci_plot_applications}}
- **Implementation:** {{sci_plot_implementation}}
- **Quality Standards:** {{sci_plot_standards}}
- **Examples:** {{sci_plot_examples}}
<</REPEAT>>

### Interactive Visualizations

^^CONDITION: includes_interactive == "yes"^^
**Interactive Elements:**
{{interactive_elements}}

**User Interface Guidelines:**
{{ui_guidelines}}

**Interaction Patterns:**
{{interaction_patterns}}

**Performance Considerations:**
{{interactive_performance}}
^^/CONDITION: includes_interactive^^

## MATLAB Implementation Guidelines

[[LLM: Provide specific guidance for implementing visualizations in MATLAB.]]

### Code Structure and Organization

**File Organization:**
{{code_organization}}

**Function Design:**
{{function_design}}

**Parameter Management:**
{{parameter_management}}

**Documentation Standards:**
{{code_documentation}}

### MATLAB Best Practices

**Plotting Functions:**
{{plotting_functions}}

**Performance Optimization:**
{{performance_optimization}}

**Memory Management:**
{{memory_management}}

**Error Handling:**
{{error_handling}}

### Custom Styling Implementation

**Style Templates:**
{{style_templates}}

**Color Management:**
{{color_management}}

**Font Configuration:**
{{font_configuration}}

**Export Settings:**
{{export_settings}}

## Quality Assurance Standards

[[LLM: Define quality standards and validation procedures for visualizations.]]

### Visual Quality Criteria

**Technical Quality:**
{{technical_quality_criteria}}

**Aesthetic Quality:**
{{aesthetic_quality_criteria}}

**Functional Quality:**
{{functional_quality_criteria}}

**Accessibility Quality:**
{{accessibility_quality_criteria}}

### Review and Validation Process

**Design Review:**
{{design_review_process}}

**Technical Review:**
{{technical_review_process}}

**User Testing:**
{{user_testing_process}}

**Final Approval:**
{{approval_process}}

## Publication and Output Standards

[[LLM: Define standards for different output formats and publication venues.]]

### Output Formats

^^CONDITION: output_type == "publication"^^
**Academic Publications:**
{{publication_standards}}

**Journal Requirements:**
{{journal_requirements}}

**Conference Standards:**
{{conference_standards}}

**Thesis/Dissertation:**
{{thesis_standards}}
^^/CONDITION: output_type^^

^^CONDITION: output_type == "presentation"^^
**Presentation Formats:**
{{presentation_standards}}

**Slide Design:**
{{slide_design}}

**Poster Requirements:**
{{poster_requirements}}

**Digital Display:**
{{digital_display}}
^^/CONDITION: output_type^^

^^CONDITION: output_type == "report"^^
**Technical Reports:**
{{report_standards}}

**Documentation:**
{{documentation_standards}}

**Web Publication:**
{{web_standards}}

**Print Requirements:**
{{print_requirements}}
^^/CONDITION: output_type^^

### Resolution and Format Specifications

**Image Resolution:**
{{image_resolution}}

**File Formats:**
{{file_formats}}

**Color Specifications:**
{{color_specifications}}

**Size Requirements:**
{{size_requirements}}

## Implementation Examples

[[LLM: Provide concrete examples of visualization implementations.]]

### Basic Plot Examples

**Example 1: {{example_1_name}}**
```matlab
{{example_1_code}}
```
**Description:** {{example_1_description}}

**Example 2: {{example_2_name}}**
```matlab
{{example_2_code}}
```
**Description:** {{example_2_description}}

### Advanced Visualization Examples

**Complex Figure: {{complex_example_name}}**
```matlab
{{complex_example_code}}
```
**Features:** {{complex_example_features}}

### Custom Function Examples

**Utility Function: {{utility_function_name}}**
```matlab
{{utility_function_code}}
```
**Purpose:** {{utility_function_purpose}}

## Troubleshooting and Common Issues

[[LLM: Address common visualization problems and solutions.]]

### Common Problems

**Performance Issues:**
{{performance_issues}}

**Display Problems:**
{{display_problems}}

**Export Issues:**
{{export_issues}}

**Compatibility Problems:**
{{compatibility_problems}}

### Solutions and Workarounds

**Performance Solutions:**
{{performance_solutions}}

**Display Fixes:**
{{display_fixes}}

**Export Solutions:**
{{export_solutions}}

**Compatibility Fixes:**
{{compatibility_fixes}}

## Maintenance and Updates

[[LLM: Define procedures for maintaining and updating the visualization guide.]]

### Version Control

**Guide Versioning:**
{{guide_versioning}}

**Change Management:**
{{change_management}}

**Update Procedures:**
{{update_procedures}}

**Documentation:**
{{maintenance_documentation}}

### Continuous Improvement

**Feedback Collection:**
{{feedback_collection}}

**Performance Monitoring:**
{{performance_monitoring}}

**Technology Updates:**
{{technology_updates}}

**Best Practice Evolution:**
{{best_practice_evolution}}

## Appendices

[[LLM: Include supporting materials and references.]]

### Appendix A: Color Palettes

**Standard Palettes:**
{{standard_palettes}}

**Accessibility Palettes:**
{{accessibility_palettes}}

**Custom Palettes:**
{{custom_palettes}}

### Appendix B: Code Templates

**Basic Templates:**
{{basic_templates}}

**Advanced Templates:**
{{advanced_templates}}

**Utility Functions:**
{{utility_functions}}

### Appendix C: Resources and References

**MATLAB Documentation:**
{{matlab_documentation}}

**Design Resources:**
{{design_resources}}

**Academic References:**
{{academic_references}}

**Tools and Software:**
{{tools_software}}

---

[[LLM: After completing the guide, suggest next steps:
1. Review and validate design principles
2. Implement example visualizations
3. Test with target audience
4. Establish review and approval processes
5. Create training materials for team members]]

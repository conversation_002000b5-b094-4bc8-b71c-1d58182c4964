%% 视频分割处理主控制脚本
% 本脚本提供用户友好的界面来选择视频分割模式并执行处理
% 支持四种不同的分割模式，使用固定的输入输出路径
%
% 作者: 视频处理模块
% 日期: 2025-08-21
% 版本: 2.0 (重构版本)

%% 清理工作空间
clear; clc; close all;

%% 脚本信息显示
fprintf('=================================================\n');
fprintf('           视频分割处理系统 v2.0\n');
fprintf('=================================================\n');
fprintf('功能: 将长视频按指定模式分割成多个短片段\n');
fprintf('支持: 批量处理、多种格式、进度显示、错误恢复\n');
fprintf('=================================================\n\n');

%% 配置固定路径
% 获取脚本所在目录
scriptDir = fileparts(mfilename('fullpath'));

% 设置相对于脚本目录的路径
inputFolder = fullfile(scriptDir, '1、Raw data');
outputFolder = fullfile(scriptDir, '2、Processed data');

fprintf('配置信息:\n');
fprintf('脚本目录: %s\n', scriptDir);
fprintf('输入文件夹: %s\n', inputFolder);
fprintf('输出文件夹: %s\n', outputFolder);

% 调试信息：显示当前工作目录
fprintf('当前工作目录: %s\n', pwd);

%% 检查和创建必要文件夹
if ~exist(inputFolder, 'dir')
    mkdir(inputFolder);
    fprintf('\n已创建输入文件夹: %s\n', inputFolder);
    fprintf('请将待处理的视频文件放入此文件夹中，然后重新运行脚本\n');
    return;
end

if ~exist(outputFolder, 'dir')
    mkdir(outputFolder);
    fprintf('已创建输出文件夹: %s\n', outputFolder);
end

%% 检查输入文件
supportedFormats = {'.mp4', '.avi', '.mov'};
videoFiles = [];

% 调试信息：检查文件夹是否存在
fprintf('\n调试信息:\n');
fprintf('输入文件夹是否存在: %s\n', mat2str(exist(inputFolder, 'dir') == 7));

for fmt = supportedFormats
    searchPattern = fullfile(inputFolder, ['*' fmt{1}]);
    fprintf('搜索模式: %s\n', searchPattern);
    files = dir(searchPattern);
    fprintf('找到 %d 个 %s 文件\n', length(files), fmt{1});
    videoFiles = [videoFiles; files]; %#ok<AGROW>
end

fprintf('总共找到 %d 个视频文件\n', length(videoFiles));

if isempty(videoFiles)
    fprintf('\n⚠️  警告: 在输入文件夹中未找到视频文件！\n');
    fprintf('支持的格式: %s\n', strjoin(supportedFormats, ', '));
    fprintf('请检查以下路径是否正确: %s\n', inputFolder);

    % 显示文件夹内容以帮助调试
    allFiles = dir(inputFolder);
    if ~isempty(allFiles)
        fprintf('\n文件夹中的所有内容:\n');
        for i = 1:length(allFiles)
            if ~allFiles(i).isdir
                fprintf('  %s\n', allFiles(i).name);
            end
        end
    end
    return;
end

fprintf('\n✅ 找到 %d 个视频文件:\n', length(videoFiles));
for i = 1:length(videoFiles)
    fprintf('  %d. %s\n', i, videoFiles(i).name);
end

%% 显示分割模式选项
fprintf('\n=== 视频分割模式选择 ===\n');

% 显示所有可用模式的详细信息
for mode = 1:4
    try
        config = getVideoSplitConfig(mode);
        fprintf('%d. %s\n', mode, config.modeName);
        fprintf('   %s\n', config.description);
        fprintf('   总时长: %d秒 (%.1f分钟) | 片段数: %d个\n\n', ...
            config.totalDuration, config.totalDuration/60, config.totalSegments);
    catch
        fprintf('%d. 模式配置错误\n\n', mode);
    end
end

%% 用户选择分割模式
while true
    choice = input('请选择分割模式 (1-4): ');
    if isnumeric(choice) && isscalar(choice) && ismember(choice, [1, 2, 3, 4])
        break;
    else
        fprintf('❌ 无效选择，请输入1-4之间的数字\n');
    end
end

%% 显示选择的模式信息
try
    selectedConfig = getVideoSplitConfig(choice);
    fprintf('\n✅ 已选择: %s\n', selectedConfig.modeName);
    fprintf('配置详情: %s\n', selectedConfig.description);
catch ME
    fprintf('❌ 获取模式配置失败: %s\n', ME.message);
    return;
end

%% 处理说明
fprintf('\n=== 处理说明 ===\n');
fprintf('📋 处理流程:\n');
fprintf('   1. 对每个视频文件显示基本信息\n');
fprintf('   2. 询问是否需要删除视频开头部分\n');
fprintf('   3. 根据选择的模式进行视频分割\n');
fprintf('   4. 显示详细的处理统计信息\n');
fprintf('\n💡 交互式预处理:\n');
fprintf('   - 每个视频可以单独设置删除时长\n');
fprintf('   - 提供时间参考信息帮助决策\n');
fprintf('   - 完整的输入验证和确认机制\n');

%% 确认开始处理
fprintf('\n=== 处理确认 ===\n');
fprintf('输入文件夹: %s\n', inputFolder);
fprintf('输出文件夹: %s\n', outputFolder);
fprintf('分割模式: %s\n', selectedConfig.modeName);
fprintf('待处理文件: %d个\n', length(videoFiles));

confirm = input('\n确认开始处理？(y/n): ', 's');
if ~strcmpi(confirm, 'y')
    fprintf('处理已取消\n');
    return;
end

%% 执行视频分割处理
fprintf('\n🚀 开始处理视频...\n');
tic; % 开始计时

try
    % 调用通用分割函数
    splitVideo(choice, inputFolder, outputFolder);
    
    processingTime = toc; % 结束计时
    
    %% 处理完成统计
    fprintf('\n=== 处理完成统计 ===\n');
    fprintf('✅ 总处理时间: %.2f 秒 (%.2f 分钟)\n', processingTime, processingTime/60);
    
    % 统计输出文件
    outputFiles = dir(fullfile(outputFolder, '*.mp4'));
    if ~isempty(outputFiles)
        fprintf('✅ 成功生成 %d 个视频片段\n', length(outputFiles));
        
        % 计算总文件大小
        totalSize = sum([outputFiles.bytes]);
        if totalSize > 1e9
            fprintf('📁 输出文件总大小: %.2f GB\n', totalSize/1e9);
        elseif totalSize > 1e6
            fprintf('📁 输出文件总大小: %.2f MB\n', totalSize/1e6);
        else
            fprintf('📁 输出文件总大小: %.2f KB\n', totalSize/1e3);
        end
        
        fprintf('📂 输出文件保存在: %s\n', fullfile(pwd, outputFolder));
    else
        fprintf('⚠️  警告: 未找到输出文件，处理可能失败\n');
    end
    
catch ME
    fprintf('❌ 处理过程中发生错误:\n');
    fprintf('错误信息: %s\n', ME.message);
    fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    return;
end

%% 显示使用建议和后续操作
fprintf('\n=== 使用建议 ===\n');
fprintf('💡 1. 建议在处理大文件前先备份原始视频\n');
fprintf('💡 2. 确保有足够的磁盘空间存储分割后的文件\n');
fprintf('💡 3. 可以根据需要调整不同的分割模式\n');
fprintf('💡 4. 处理完成后可以删除不需要的片段以节省空间\n');
fprintf('💡 5. 分割后的文件可用于后续的信号处理分析\n');

%% 询问是否查看输出文件夹
viewFolder = input('\n是否打开输出文件夹查看结果？(y/n): ', 's');
if strcmpi(viewFolder, 'y')
    try
        if ispc
            winopen(outputFolder);
        elseif ismac
            system(['open "' outputFolder '"']);
        else
            system(['xdg-open "' outputFolder '"']);
        end
        fprintf('✅ 已打开输出文件夹\n');
    catch
        fprintf('⚠️  无法自动打开文件夹，请手动查看: %s\n', outputFolder);
    end
end

%% 脚本结束
fprintf('\n🎉 视频分割处理完成！\n');
fprintf('感谢使用视频分割处理系统 v2.0\n');
fprintf('=================================================\n');

# MATLAB Code Quality Checklist

[[LLM: This checklist provides comprehensive quality assessment for MATLAB code. Work through each section systematically, providing specific feedback and ratings for each item.]]

## Required Artifacts

- MATLAB code files (.m, .mlx, .mlapp)
- Documentation and comments
- Test files and test results
- Performance analysis (if applicable)

## Section 1: Code Structure and Organization (Weight: 25%)

[[LLM: Evaluate the overall structure and organization of the MATLAB code. Consider modularity, clarity, and maintainability.]]

### 1.1 File Organization
- [ ] Files are properly organized in logical directory structure
- [ ] Function files contain only one main function (unless subfunctions are appropriate)
- [ ] Script files have clear purpose and scope
- [ ] File names follow MATLAB naming conventions

### 1.2 Function Design
- [ ] Functions have single, well-defined purpose
- [ ] Function interfaces are clean and minimal
- [ ] Input/output arguments are clearly defined
- [ ] Function length is reasonable (typically < 50-100 lines)

### 1.3 Code Modularity
- [ ] Code is appropriately modularized
- [ ] Reusable components are identified and extracted
- [ ] Dependencies between modules are minimized
- [ ] Common functionality is not duplicated

**Section 1 Rating:** ⭐⭐⭐⭐⭐ (1-5 stars)
**Section 1 Comments:** {{section_1_comments}}

## Section 2: MATLAB Best Practices (Weight: 30%)

[[LLM: Assess adherence to MATLAB-specific best practices and conventions.]]

### 2.1 Vectorization
- [ ] Loops are vectorized where appropriate
- [ ] Built-in MATLAB functions are used instead of manual implementations
- [ ] Array operations are used efficiently
- [ ] Unnecessary loops are avoided

### 2.2 Memory Management
- [ ] Arrays are preallocated when size is known
- [ ] Memory usage is optimized
- [ ] Large temporary variables are cleared when no longer needed
- [ ] Memory-efficient data types are used

### 2.3 MATLAB Function Usage
- [ ] Appropriate MATLAB built-in functions are used
- [ ] Toolbox functions are used correctly
- [ ] Function calls are efficient and appropriate
- [ ] Deprecated functions are avoided

### 2.4 Data Types and Structures
- [ ] Appropriate data types are used (double, single, int, logical)
- [ ] Cell arrays and structures are used appropriately
- [ ] Data type conversions are handled properly
- [ ] Sparse matrices are used when beneficial

**Section 2 Rating:** ⭐⭐⭐⭐⭐ (1-5 stars)
**Section 2 Comments:** {{section_2_comments}}

## Section 3: Code Quality and Readability (Weight: 20%)

[[LLM: Evaluate code readability, naming conventions, and overall quality.]]

### 3.1 Naming Conventions
- [ ] Variable names are descriptive and meaningful
- [ ] Function names clearly indicate their purpose
- [ ] Naming follows MATLAB conventions (camelCase for variables/functions)
- [ ] Constants are appropriately named and defined

### 3.2 Code Formatting
- [ ] Consistent indentation is used
- [ ] Code is properly formatted and readable
- [ ] Line length is reasonable (< 80-100 characters)
- [ ] Whitespace is used effectively for readability

### 3.3 Code Clarity
- [ ] Code logic is clear and easy to follow
- [ ] Complex operations are broken down into understandable steps
- [ ] Magic numbers are avoided or properly explained
- [ ] Code is self-documenting where possible

**Section 3 Rating:** ⭐⭐⭐⭐⭐ (1-5 stars)
**Section 3 Comments:** {{section_3_comments}}

## Section 4: Documentation and Comments (Weight: 15%)

[[LLM: Assess the quality and completeness of documentation and comments.]]

### 4.1 Function Documentation
- [ ] All functions have proper header documentation
- [ ] Input and output arguments are documented
- [ ] Function purpose and behavior are clearly explained
- [ ] Examples of usage are provided where helpful

### 4.2 Inline Comments
- [ ] Complex code sections are commented
- [ ] Comments explain "why" not just "what"
- [ ] Comments are accurate and up-to-date
- [ ] Comment density is appropriate (not too sparse or verbose)

### 4.3 Help Documentation
- [ ] Functions provide proper help text accessible via 'help' command
- [ ] Help text follows MATLAB documentation standards
- [ ] Examples are included in help text where appropriate
- [ ] See-also references are provided where relevant

**Section 4 Rating:** ⭐⭐⭐⭐⭐ (1-5 stars)
**Section 4 Comments:** {{section_4_comments}}

## Section 5: Error Handling and Robustness (Weight: 10%)

[[LLM: Evaluate error handling, input validation, and code robustness.]]

### 5.1 Input Validation
- [ ] Function inputs are validated appropriately
- [ ] Input types and ranges are checked
- [ ] Meaningful error messages are provided
- [ ] Edge cases are handled properly

### 5.2 Error Handling
- [ ] Appropriate error handling mechanisms are used
- [ ] try-catch blocks are used where appropriate
- [ ] Error messages are informative and actionable
- [ ] Graceful degradation is implemented where possible

### 5.3 Robustness
- [ ] Code handles unexpected inputs gracefully
- [ ] Numerical stability is considered
- [ ] Boundary conditions are properly handled
- [ ] Code fails safely when errors occur

**Section 5 Rating:** ⭐⭐⭐⭐⭐ (1-5 stars)
**Section 5 Comments:** {{section_5_comments}}

## Overall Assessment

[[LLM: Calculate overall score and provide comprehensive summary.]]

**Overall Quality Score:** {{overall_score}}/5 ⭐
**Weighted Score Calculation:**
- Section 1 (25%): {{section_1_score}} × 0.25 = {{section_1_weighted}}
- Section 2 (30%): {{section_2_score}} × 0.30 = {{section_2_weighted}}
- Section 3 (20%): {{section_3_score}} × 0.20 = {{section_3_weighted}}
- Section 4 (15%): {{section_4_score}} × 0.15 = {{section_4_weighted}}
- Section 5 (10%): {{section_5_score}} × 0.10 = {{section_5_weighted}}

**Total Weighted Score:** {{total_weighted_score}}

## Key Findings

### Strengths
{{code_strengths}}

### Areas for Improvement
{{improvement_areas}}

### Critical Issues
{{critical_issues}}

## Recommendations

### Immediate Actions Required
{{immediate_actions}}

### Performance Improvements
{{performance_improvements}}

### Long-term Enhancements
{{longterm_enhancements}}

## Quality Gate Decision

[[LLM: Make a clear recommendation based on the assessment.]]

**Quality Gate Status:** {{quality_gate_status}}
- ✅ **PASS** - Code meets quality standards and is ready for use
- ⚠️ **CONDITIONAL PASS** - Code is acceptable with minor improvements
- ❌ **FAIL** - Code requires significant improvements before acceptance

**Justification:** {{quality_gate_justification}}

**Next Steps:** {{next_steps}}

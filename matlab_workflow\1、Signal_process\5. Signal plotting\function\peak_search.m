function [peak_indices, peak_values, envelope, peak_times] = peak_search(signal, fs)
    % 峰值搜索函数，用于检测信号中的肠鸣音峰值
    % 输入：
    %   signal - 增强后的语音信号
    %   fs - 采样频率
    % 输出：
    %   peak_indices - 检测到的峰值索引
    %   peak_values - 检测到的峰值值
    %   envelope - 信号的包络
    %   peak_times - 每个峰值对应的时间刻度

    % 设置峰值检测参数
    minPeakHeight = 0.05;  % 最小峰值高度
    minPeakDistance = 0.1 * fs;   % 将最小峰值间距设为 100 毫秒对应的采样点数

    % 对信号进行去趋势处理
    signal_detrend = detrend(signal);

    % 计算信号的包络
    envelope = abs(hilbert(signal_detrend));

    % 计算时间轴
    t = (0:length(signal)-1)' / fs;  % 转置为列向量

    % 使用findpeaks函数在包络上检测峰值
    [pks, locs] = findpeaks(envelope, 'MinPeakHeight', minPeakHeight, 'MinPeakDistance', minPeakDistance);

    % 计算每个峰值对应的时间刻度
    peak_times = locs / fs;

    % 绘制信号、包络和检测到的峰值
    figure('Position', [100, 100, 1000, 600]);
    plot(t, signal, 'k');
    hold on;
    plot(t, envelope, 'g');
    plot(t(locs), pks, 'ro', 'MarkerSize', 5);
    hold off;
    xlabel('时间 (秒)');
    ylabel('幅值');
    title('信号、包络和检测到的峰值');
    legend('信号', '包络', '峰值');
    grid on;

    % 输出峰值索引和峰值
    peak_indices = locs;
    peak_values = pks;
end




% function [peak_indices, peak_values, envelope] = peak_search(signal, fs)
%     % 峰值搜索函数，用于检测信号中的肠鸣音峰值
%     % 输入：
%     %   signal - 增强后的语音信号
%     %   fs - 采样频率
%     % 输出：
%     %   peak_indices - 检测到的峰值索引
%     %   peak_values - 检测到的峰值值
%     %   envelope - 信号的包络
% 
%     % 设置峰值检测参数
%     minPeakHeight = 0.02;  % 最小峰值高度
%     minPeakDistance = 0.1 * fs;   % 将最小峰值间距设为 0.5 秒对应的采样点数
% 
%     % 对信号进行去趋势处理
%     signal_detrend = detrend(signal);
% 
%     % 计算信号的包络
%     envelope = abs(hilbert(signal_detrend));
% 
%     % 计算时间轴
%     t = (0:length(signal)-1)' / fs;  % 转置为列向量
% 
%     % 使用findpeaks函数在包络上检测峰值
%     [pks, locs] = findpeaks(envelope, 'MinPeakHeight', minPeakHeight, 'MinPeakDistance', minPeakDistance);
% 
%     % 绘制信号、包络和检测到的峰值
%     figure('Position', [100, 100, 1000, 600]);
%     plot(t, signal, 'k');
%     hold on;
%     plot(t, envelope, 'g');
%     plot(t(locs), pks, 'ro', 'MarkerSize', 5);
%     hold off;
%     xlabel('时间 (秒)');
%     ylabel('幅值');
%     title('信号、包络和检测到的峰值');
%     legend('信号', '包络', '峰值');
%     grid on;
% 
%     % 输出峰值索引和峰值
%     peak_indices = locs;
%     peak_values = pks;
% end
% 
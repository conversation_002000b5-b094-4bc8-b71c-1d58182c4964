function config = getVideoSplitConfig(mode)
%GETVIDEOSPLITCONFIG 获取视频分割模式配置参数
%   该函数根据指定的分割模式返回相应的配置参数，包括片段时长、
%   片段数量等信息。所有模式都设计为总时长约300秒（5分钟）。
%
%   语法:
%   config = getVideoSplitConfig(mode)
%
%   输入参数:
%   mode - 分割模式编号 (整数: 1-4)
%          1: 9秒模式  - 33个9秒片段 + 1个3秒片段
%          2: 18秒模式 - 16个18秒片段 + 1个12秒片段  
%          3: 24秒模式 - 12个24秒片段 + 1个12秒片段
%          4: 60秒模式 - 5个60秒片段
%
%   输出参数:
%   config - 配置结构体，包含以下字段:
%            .normalDuration    - 常规片段时长（秒）
%            .shortDuration     - 短片段时长（秒），如果没有则为0
%            .normalCount       - 常规片段数量
%            .shortCount        - 短片段数量
%            .totalSegments     - 总片段数量
%            .totalDuration     - 总时长（秒）
%            .modeName          - 模式名称
%            .description       - 模式描述
%
%   示例:
%   config = getVideoSplitConfig(1);  % 获取9秒模式配置
%   config = getVideoSplitConfig(2);  % 获取18秒模式配置
%
%   注意事项:
%   - 所有模式的总时长都设计为约300秒
%   - 模式4（60秒）没有短片段，只有常规片段
%   - 输入的mode参数必须在1-4范围内
%
%   另请参阅: SPLITVIDEO, VIDEOSPLITMAIN

% 输入参数验证
if nargin < 1
    error('getVideoSplitConfig:MissingInput', '必须提供分割模式参数');
end

if ~isnumeric(mode) || ~isscalar(mode) || mode ~= round(mode) || mode < 1 || mode > 4
    error('getVideoSplitConfig:InvalidMode', ...
        '分割模式必须是1-4之间的整数\n1: 9秒模式\n2: 18秒模式\n3: 24秒模式\n4: 60秒模式');
end

% 根据模式设置配置参数
switch mode
    case 1  % 9秒模式：33个9秒片段 + 1个3秒片段 = 300秒
        config.normalDuration = 9;
        config.shortDuration = 3;
        config.normalCount = 33;
        config.shortCount = 1;
        config.totalSegments = 34;
        config.totalDuration = 33 * 9 + 1 * 3;  % 300秒
        config.modeName = '9秒模式';
        config.description = '33个9秒片段 + 1个3秒片段，适合短时分析和快速预览';
        
    case 2  % 18秒模式：16个18秒片段 + 1个12秒片段 = 300秒
        config.normalDuration = 18;
        config.shortDuration = 12;
        config.normalCount = 16;
        config.shortCount = 1;
        config.totalSegments = 17;
        config.totalDuration = 16 * 18 + 1 * 12;  % 300秒
        config.modeName = '18秒模式';
        config.description = '16个18秒片段 + 1个12秒片段，适合中等时长分析';
        
    case 3  % 24秒模式：12个24秒片段 + 1个12秒片段 = 300秒
        config.normalDuration = 24;
        config.shortDuration = 12;
        config.normalCount = 12;
        config.shortCount = 1;
        config.totalSegments = 13;
        config.totalDuration = 12 * 24 + 1 * 12;  % 300秒
        config.modeName = '24秒模式';
        config.description = '12个24秒片段 + 1个12秒片段，适合详细分析';
        
    case 4  % 60秒模式：5个60秒片段 = 300秒
        config.normalDuration = 60;
        config.shortDuration = 0;  % 没有短片段
        config.normalCount = 5;
        config.shortCount = 0;
        config.totalSegments = 5;
        config.totalDuration = 5 * 60;  % 300秒
        config.modeName = '60秒模式';
        config.description = '5个60秒片段，适合长时段分析和深度研究';
        
    otherwise
        error('getVideoSplitConfig:UnsupportedMode', '不支持的分割模式: %d', mode);
end

% 添加通用配置信息
config.mode = mode;
config.videoQuality = 95;  % 视频质量设置
config.videoFormat = 'MPEG-4';  % 输出视频格式
config.supportedInputFormats = {'.mp4', '.avi', '.mov'};  % 支持的输入格式

% 验证配置的一致性
if config.totalDuration ~= 300
    warning('getVideoSplitConfig:DurationMismatch', ...
        '模式 %d 的总时长为 %d 秒，不等于预期的300秒', mode, config.totalDuration);
end

% 显示配置信息（可选）
if nargout == 0
    fprintf('\n=== 视频分割模式配置 ===\n');
    fprintf('模式: %s\n', config.modeName);
    fprintf('描述: %s\n', config.description);
    fprintf('常规片段: %d个 × %d秒\n', config.normalCount, config.normalDuration);
    if config.shortCount > 0
        fprintf('短片段: %d个 × %d秒\n', config.shortCount, config.shortDuration);
    end
    fprintf('总片段数: %d个\n', config.totalSegments);
    fprintf('总时长: %d秒 (%.1f分钟)\n', config.totalDuration, config.totalDuration/60);
    fprintf('========================\n');
end

end

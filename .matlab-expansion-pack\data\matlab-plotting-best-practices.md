# MATLAB Plotting Best Practices Guide

## Figure Setup and Configuration

### Figure Creation and Sizing
```matlab
% Create figure with specific size and properties
figure('Units', 'inches', 'Position', [1 1 8 6]);
set(gcf, 'PaperPositionMode', 'auto');
set(gcf, 'Color', 'white');
set(gcf, 'Renderer', 'painters'); % For vector graphics
```

### Default Settings Configuration
```matlab
% Set publication-quality defaults
set(groot, 'defaultAxesFontName', 'Arial');
set(groot, 'defaultAxesFontSize', 12);
set(groot, 'defaultTextFontName', 'Arial');
set(groot, 'defaultTextFontSize', 12);
set(groot, 'defaultLineLineWidth', 1.5);
set(groot, 'defaultAxesLineWidth', 1);
```

## Color Management

### Professional Color Palettes
```matlab
% MATLAB default colors (improved)
colors = [0 0.4470 0.7410;     % Blue
          0.8500 0.3250 0.0980; % Red
          0.9290 0.6940 0.1250; % Yellow
          0.4940 0.1840 0.5560; % Purple
          0.4660 0.6740 0.1880; % Green
          0.3010 0.7450 0.9330; % Cyan
          0.6350 0.0780 0.1840]; % Dark Red

% Colorblind-friendly palette
cb_colors = [0.000 0.447 0.741;  % Blue
             0.850 0.325 0.098;  % Vermillion
             0.929 0.694 0.125;  % Yellow
             0.494 0.184 0.556;  % Purple
             0.466 0.674 0.188;  % Green
             0.301 0.745 0.933;  % Sky Blue
             0.635 0.078 0.184]; % Dark Red
```

### Color Usage Guidelines
- Use consistent colors across related figures
- Limit color palette to 7-8 distinct colors maximum
- Ensure sufficient contrast for accessibility
- Test visualizations in grayscale for print compatibility
- Use color to enhance, not replace, other visual cues

## Typography and Text

### Font Selection and Sizing
```matlab
% Recommended font sizes for different elements
title_size = 16;
axis_label_size = 14;
tick_label_size = 12;
legend_size = 12;
annotation_size = 10;

% Apply consistent typography
title('Figure Title', 'FontSize', title_size, 'FontWeight', 'bold');
xlabel('X-axis Label', 'FontSize', axis_label_size);
ylabel('Y-axis Label', 'FontSize', axis_label_size);
```

### Mathematical Notation
```matlab
% Use LaTeX for mathematical expressions
xlabel('Time $t$ (seconds)', 'Interpreter', 'latex');
ylabel('$\alpha$ coefficient', 'Interpreter', 'latex');
title('$f(x) = \frac{1}{\sqrt{2\pi\sigma^2}} e^{-\frac{(x-\mu)^2}{2\sigma^2}}$', ...
      'Interpreter', 'latex');
```

## Plot Types and Best Practices

### Line Plots
```matlab
% Effective line plot styling
plot(x, y1, 'LineWidth', 2, 'Color', colors(1,:));
hold on;
plot(x, y2, '--', 'LineWidth', 2, 'Color', colors(2,:));
plot(x, y3, ':', 'LineWidth', 2, 'Color', colors(3,:));

% Add markers for sparse data
plot(x_sparse, y_sparse, 'o', 'MarkerSize', 8, 'MarkerFaceColor', colors(1,:), ...
     'MarkerEdgeColor', 'black', 'LineWidth', 1.5);
```

### Scatter Plots
```matlab
% Professional scatter plot
scatter(x, y, 50, z, 'filled', 'MarkerEdgeColor', 'black', 'LineWidth', 0.5);
colormap(viridis); % Use perceptually uniform colormap
colorbar('FontSize', 12);
```

### Bar Charts
```matlab
% Clean bar chart design
bar(categories, values, 'FaceColor', colors(1,:), 'EdgeColor', 'black', ...
    'LineWidth', 1);
set(gca, 'XTickLabelRotation', 45); % Rotate labels if needed
```

### Error Bars and Uncertainty
```matlab
% Proper error bar representation
errorbar(x, y, err_lower, err_upper, 'o', 'LineWidth', 1.5, ...
         'MarkerSize', 6, 'MarkerFaceColor', colors(1,:), ...
         'Color', colors(1,:), 'CapSize', 4);
```

## Axes Configuration

### Axis Limits and Scaling
```matlab
% Set appropriate axis limits
xlim([min(x)*0.95, max(x)*1.05]); % Add 5% padding
ylim([0, max(y)*1.1]); % Start from zero if meaningful

% Use log scale when appropriate
set(gca, 'XScale', 'log', 'YScale', 'log');
```

### Grid and Tick Configuration
```matlab
% Professional grid styling
grid on;
set(gca, 'GridAlpha', 0.3, 'GridLineStyle', '-');
set(gca, 'MinorGridAlpha', 0.1);

% Custom tick configuration
xticks(0:10:100);
xticklabels({'0', '10', '20', '30', '40', '50', '60', '70', '80', '90', '100'});
```

## Legends and Annotations

### Legend Best Practices
```matlab
% Create informative legend
legend({'Dataset 1', 'Dataset 2', 'Model Prediction'}, ...
       'Location', 'best', 'FontSize', 12, 'Box', 'off');

% Position legend manually if needed
legend('Location', 'northwest');
legend('Position', [0.15 0.75 0.2 0.15]); % [x y width height]
```

### Annotations and Text
```matlab
% Add informative annotations
text(x_pos, y_pos, 'Important Point', 'FontSize', 12, ...
     'HorizontalAlignment', 'center', 'BackgroundColor', 'white', ...
     'EdgeColor', 'black', 'Margin', 2);

% Arrow annotations
annotation('arrow', [0.3 0.4], [0.6 0.7], 'LineWidth', 2);
```

## Subplots and Multi-Panel Figures

### Subplot Organization
```matlab
% Create well-organized subplots
figure('Position', [100 100 1200 800]);

subplot(2, 2, 1);
% Plot 1 code here
title('(a) First Analysis');

subplot(2, 2, 2);
% Plot 2 code here
title('(b) Second Analysis');

% Adjust spacing
sgtitle('Overall Figure Title', 'FontSize', 16, 'FontWeight', 'bold');
```

### Consistent Styling Across Subplots
```matlab
% Function to apply consistent styling
function style_subplot(ax)
    set(ax, 'FontSize', 12);
    set(ax, 'LineWidth', 1);
    grid(ax, 'on');
    set(ax, 'GridAlpha', 0.3);
end

% Apply to all subplots
for i = 1:4
    subplot(2, 2, i);
    % Plot code here
    style_subplot(gca);
end
```

## Performance Optimization

### Large Dataset Handling
```matlab
% Downsample for display if necessary
if length(x) > 10000
    idx = 1:10:length(x); % Every 10th point
    plot(x(idx), y(idx), 'LineWidth', 1.5);
else
    plot(x, y, 'LineWidth', 1.5);
end
```

### Memory Efficient Plotting
```matlab
% Use appropriate data types
x = single(x); % Use single precision if double not needed
y = single(y);

% Clear unnecessary variables
clear temp_data intermediate_results;
```

## Export and Output

### High-Quality Export Settings
```matlab
% Vector format export (preferred for publications)
print(gcf, 'figure1', '-depsc', '-r600'); % EPS format
print(gcf, 'figure1', '-dpdf', '-r600');  % PDF format

% Raster format export (for presentations)
print(gcf, 'figure1', '-dpng', '-r300');  % PNG format
exportgraphics(gcf, 'figure1.png', 'Resolution', 300);
```

### Publication-Ready Export
```matlab
% Configure for publication
set(gcf, 'PaperUnits', 'inches');
set(gcf, 'PaperSize', [8.5 11]); % Letter size
set(gcf, 'PaperPosition', [0.25 0.25 8 10.5]);

% Export with specific settings
print(gcf, '-dpdf', '-r600', 'publication_figure.pdf');
```

## Interactive Features

### Basic Interactivity
```matlab
% Add data cursor mode
datacursormode on;

% Enable zoom and pan
zoom on; % or pan on;

% Custom callback for interactivity
set(gcf, 'WindowButtonDownFcn', @mouseClickCallback);
```

### GUI Integration
```matlab
% Create interactive controls
uicontrol('Style', 'slider', 'Min', 0, 'Max', 100, 'Value', 50, ...
          'Position', [20 20 200 20], 'Callback', @updatePlot);
```

## Common Pitfalls to Avoid

### Visual Design Issues
- Avoid 3D effects unless truly representing 3D data
- Don't use too many colors or line styles
- Avoid pie charts for more than 5-6 categories
- Don't truncate y-axis unless clearly indicated
- Avoid rainbow colormaps (use perceptually uniform ones)

### Technical Issues
- Don't ignore aspect ratios for spatial data
- Avoid overlapping text and labels
- Don't use default MATLAB colors for publications
- Avoid bitmap formats for vector graphics
- Don't ignore colorblind accessibility

### Data Representation Issues
- Don't use inappropriate chart types for data
- Avoid misleading scales or proportions
- Don't omit error bars or uncertainty measures
- Avoid cherry-picking data ranges
- Don't ignore statistical significance

## Advanced Techniques

### Custom Colormaps
```matlab
% Create custom colormap
n = 256;
custom_cmap = [linspace(0,1,n)' linspace(0,0.8,n)' linspace(1,0,n)'];
colormap(custom_cmap);
```

### Animation and Dynamic Plots
```matlab
% Create animated plot
for i = 1:length(time_steps)
    plot(x, y(:,i), 'LineWidth', 2);
    title(sprintf('Time = %.2f s', time_steps(i)));
    drawnow;
    pause(0.1);
end
```

### Custom Plot Functions
```matlab
function h = publication_plot(x, y, varargin)
% PUBLICATION_PLOT Create publication-quality plot
    h = plot(x, y, varargin{:});
    set(h, 'LineWidth', 2);
    set(gca, 'FontSize', 12, 'LineWidth', 1);
    grid on;
    set(gca, 'GridAlpha', 0.3);
end
```

## Quality Assurance Checklist

### Before Finalizing
- [ ] Check data accuracy and representation
- [ ] Verify color accessibility (colorblind-friendly)
- [ ] Test in grayscale for print compatibility
- [ ] Ensure consistent styling across figures
- [ ] Validate export quality and resolution
- [ ] Review text readability at target size
- [ ] Check for overlapping elements
- [ ] Verify legend and label accuracy

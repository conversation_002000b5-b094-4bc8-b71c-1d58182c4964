2+2
2+2
2+2
a<-2+4
install.packages("ggplot2")
library(ggplot2)
install.packages("ggplot2")
install.packages("ggplot2")
"""
测试时间：2024年05月13日
作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。
获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。
"""
"""
测试时间：2024年05月13日
作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。
获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。
"""
library(tidyverse)
install.packages("tidyverse")
library(tidyverse)
install.packages("lubridate")
library(tidyverse)
library(ggtext)
install.packages("ggtext")
library(ggtext)
install.packages("hrbrthemes")
library(hrbrthemes)
install.packages("ggsci")
library(ggsci)
install.packages("ggpubr")
install.packages("ggprism")
library(ggprism)
install.packages("readxl")
library(readxl)
library(ggpubr)
library(tidyverse)
library(ggtext)
library(hrbrthemes)
library(ggsci)
library(ggpubr)
library(ggprism)
library(readxl)
#读取数据
heat_data_01 <- read_excel("\\第4章 双变量图形的绘制\\热力图数据01.xlsx")
#读取数据
heat_data_01 <- read_excel("\\第4章 双变量图形的绘制\\热力图数据01.xlsx")
1+1
library(ggplot2)
install.packages(c("ggplot2", "dplyr", "readr", "scales", "RColorBrewer"))
plot_mvtr_bar("results.csv")
library(readr)
library(scales)
source("plot_mvtr_bar.R")
"""
测试时间：2024年05月08日
作者: 宁海涛，代码运行出错或者因包版本更新出错，请关注微信公众号【DataCharm】进行实时获取更新。
获取书籍讲解视频和Jupyter Notebook文件等多个学习资源，可参加书籍付费学习圈子
添加微信：yidianshuyulove  备注：R书籍圈子，了解更多关于学习圈子的内容。
"""
breaks <- seq(0,1.5,by = 0.1)
####################################图3-1-1（a）使用ggplot2包的geom_histogram() 函数 绘制的直方图
ggplot() +
geom_histogram(data = hist_data,aes(hist_data),breaks = breaks,
fill="gray",colour="black",linewidth=0.3) +
scale_y_continuous(expand = c(0, 0), limits = c(0, 2500),
breaks = seq(0, 2500, by = 500)) +
scale_x_continuous(breaks = breaks) +
theme_classic()+
theme(
text = element_text(family = "serif",size = 18),
axis.text = element_text(colour = "black"),
axis.ticks.length=unit(.2, "cm"),
axis.ticks = element_line(linewidth = .4),
axis.line = element_line(linewidth = .4))
ggsave(filename = "\\第三章 单变量图表绘制\\图3-1-1 直方图示例_a.png",
width = 5.5, height = 4, dpi = 900,device=png)
install.packages("ggsave")
library(ggplot2)
ggsave(filename = "\\第三章 单变量图表绘制\\图3-1-1 直方图示例_a.pdf",
width = 5.5, height = 4,device = cairo_pdf)
setwd("D:/PhD/Code/Matlab/work/matlab_workflow/2、Equipment_test/7、透气性测试/5. Signal plotting/R")
library(ggplot2)
library(dplyr)
library(readr)
library(scales)
library(RColorBrewer)
# 创建渐变主题柱状图
plot_mvtr_bar_advanced <- function(csv_file_path) {
# 读取数据
data <- read_csv(csv_file_path, locale = locale(encoding = "UTF-8"))
materials <- data[[1]]
values <- data[[ncol(data)]]
plot_data <- data.frame(
Material = as.character(materials),
Value = as.numeric(values)
)
# 创建渐变主题图表
p <- ggplot(plot_data, aes(y = Value, fill = Value)) +
# 柱状图样式设置
geom_col(alpha = 0.9,    # 透明度：0-1，0完全透明，1完全不透明
width = 0.7) +  # 柱状图宽度：0-1，数值越大柱子越宽
# 颜色设置：渐变色配置
scale_fill_gradient(low = "#E3F2FD",    # 渐变起始色（浅色，对应低数值）
high = "#1976D2",    # 渐变结束色（深色，对应高数值）
guide = "none") +    # 不显示颜色图例
# Y轴范围设置：让Y轴0值与X轴重合
scale_y_continuous(limits = c(0, 1.2),              # Y轴范围：c(最小值, 最大值)，可改为c(0, 50)、c(10, 80)等
expand = expansion(mult = c(0, 0.05))) +  # 下方无扩展，上方5%扩展
# 基础主题设置：使用theme_bw()显示完整边框
theme_bw() +
theme(
# 背景颜色设置
plot.background = element_rect(fill = "white", color = NA),    # 整体背景色
panel.background = element_rect(fill = "white", color = NA),   # 绘图区背景色
# 字体设置：全局字体配置
text = element_text(family = "Times New Roman",  # 字体类型：可改为"Arial", "Helvetica", "SimSun"等
color = "black"),             # 全局文本颜色：可改为其他颜色如"#333333"
# 坐标轴文字设置
axis.text.x = element_text(angle = 0,        # X轴文字角度：0水平，45倾斜，90垂直
hjust = 0.5,      # 水平对齐：0左对齐，0.5居中，1右对齐
size = 11,        # X轴文字大小：可调整为8-16
color = "black"), # X轴文字颜色
axis.text.y = element_text(size = 10,        # Y轴文字大小：可调整为8-16
color = "black"), # Y轴文字颜色
# 坐标轴标题设置
axis.title = element_text(size = 12,         # 轴标题字号：可调整为10-18
face = "bold",     # 字体粗细：normal普通，bold粗体
color = "black"),  # 轴标题颜色
# 网格线设置
panel.grid = element_blank(),  # 移除所有网格线，如需网格线可设为element_line()
# 完整边框设置：使用面板边框（四个边框等粗）
axis.line = element_blank(),              # 移除轴线，避免与面板边框重复
# 面板边框设置（theme_bw()默认提供，这里自定义调整）
panel.border = element_rect(color = "black",  # 边框颜色
fill = NA,         # 边框内部不填充
linewidth = 0.6),  # 边框粗细，统一设置
# 坐标轴刻度线设置
axis.ticks = element_line(color = "black",    # 刻度线颜色
linewidth = 0.6),   # 刻度线粗细：与边框保持一致
axis.ticks.length = unit(-0.2, "cm"),         # 刻度线长度：正值朝外，负值朝内
# 布局设置：图表边距
plot.margin = margin(10, 10, 0, 10)  # 边距：上右下左，可调整为5-20
)
# 添加通用元素
p <- p +
# 坐标轴数据映射：按数值大小排序
aes(x = reorder(Material, Value)) +  # 可改为reorder(Material, -Value)实现降序排列
# 坐标轴标签设置
labs(x = "Substate Type",        # X轴标签文字：可自定义为任意文字
y = "MVTR (g/(m²·24h))") +  # Y轴标签文字：可自定义单位和格式
# 数值标签设置：柱状图顶部的数字
geom_text(aes(label = sprintf("%.2f", Value)),  # 数值格式：%.2f保留2位小数，%.1f保留1位
vjust = -0.5,          # 垂直位置：-0.5在柱子上方，0.5在柱子下方，0在柱子中间
size = 4,              # 数值标签字号：可调整为2-6
fontface = "plain",      # 字体粗细：plain普通，bold粗体，italic斜体
color = "black",       # 数值标签颜色：可改为其他颜色
family = "Times New Roman")  # 数值标签字体：与全局字体保持一致
# 显示图表
print(p)
# 返回图表对象（可用于进一步修改或保存）
return(p)
}
# ========== 使用示例 ==========
# 基本使用：读取CSV文件并绘制图表
plot_mvtr_bar_advanced("results.csv")
# 常见自定义修改示例：
# 1. 修改颜色：将渐变色改为红色系
#    scale_fill_gradient(low = "#FFEBEE", high = "#D32F2F", guide = "none")
# 2. 修改字体：将Times New Roman改为Arial
#    text = element_text(family = "Arial", color = "black")
# 3. 调整柱子宽度：将0.7改为0.5（更窄）或0.9（更宽）
#    geom_col(alpha = 0.9, width = 0.5)
# 4. 修改边框粗细：将0.4改为1.0（更粗）
#    panel.border = element_rect(color = "black", fill = NA, linewidth = 1.0)
#    axis.line = element_line(color = "black", linewidth = 1.0)
#    axis.ticks = element_line(color = "black", linewidth = 1.0)
# 5. 调整数值标签位置：将-0.5改为-0.3（更靠近柱子）
#    vjust = -0.3
# 6. 修改Y轴范围：将c(0, 100)改为其他范围
#    limits = c(0, 50)：设置Y轴范围为0到50
#    limits = c(10, 80)：设置Y轴范围为10到80
#    limits = c(0, NA)：设置最小值为0，最大值自动调整
#    limits = c(NA, 100)：设置最大值为100，最小值自动调整
# 7. 完整边框说明：
#    - theme_bw() 提供完整的四边框
#    - panel.border 控制外边框
#    - axis.line 控制坐标轴线（在theme_bw()中通常被panel.border覆盖）
#    - 所有linewidth参数保持一致可确保边框等粗
# 加载必要的包
library(readr)
# 加载 plotBarChart 模块
source("plotBarChart.R")
# 创建渐变主题柱状图
plot_mvtr_bar_advanced <- function(csv_file_path) {
# 读取数据
data <- read_csv(csv_file_path, locale = locale(encoding = "UTF-8"))
materials <- data[[1]]
values <- data[[ncol(data)]]
# 调用 plotBarChart 函数
p <- plotBarChart(materials = materials,
values = values)
# 显示图表
print(p)
# 返回图表对象（可用于进一步修改或保存）
return(p)
}
# ========== 使用示例 ==========
# 基本使用：读取CSV文件并绘制图表
# plot_mvtr_bar_advanced("results.csv")
# ========== 自定义参数说明 ==========
# 如需自定义样式，可以直接调用 plotBarChart 函数：
# plotBarChart(materials = c("Material A", "Material B"),
#              values = c(0.5, 1.2),
#              gradient_low = "#FFEBEE",     # 修改渐变起始色
#              gradient_high = "#D32F2F",    # 修改渐变结束色
#              y_limits = c(0, 2.0),         # 修改Y轴范围
#              value_format = "%.1f")        # 修改数值格式
# 加载必要的包
library(readr)
# 加载 plotBarChart 模块
source("plotBarChart.R")
# 创建渐变主题柱状图
plot_mvtr_bar_advanced <- function(csv_file_path) {
# 读取数据
data <- read_csv(csv_file_path, locale = locale(encoding = "UTF-8"))
materials <- data[[1]]
values <- data[[ncol(data)]]
# 调用 plotBarChart 函数
p <- plotBarChart(materials = materials,
values = values)
# 显示图表
print(p)
# 返回图表对象（可用于进一步修改或保存）
return(p)
}
# ========== 使用示例 ==========
# 基本使用：读取CSV文件并绘制图表
# plot_mvtr_bar_advanced("results.csv")
# ========== 自定义参数说明 ==========
# 如需自定义样式，可以直接调用 plotBarChart 函数：
# plotBarChart(materials = c("Material A", "Material B"),
#              values = c(0.5, 1.2),
#              gradient_low = "#FFEBEE",     # 修改渐变起始色
#              gradient_high = "#D32F2F",    # 修改渐变结束色
#              y_limits = c(0, 2.0),         # 修改Y轴范围
#              value_format = "%.1f")        # 修改数值格式
# ==========================================
# 文件名：plotBarChart.R
# 主要功能：绘制渐变色柱状图（MVTR数据可视化）
# 依赖文件：无
# 主要函数：
#   - plotBarChart(): 绘制材料-数值渐变色柱状图
# 输入参数：材料名称向量，数值向量，样式参数
# 输出结果：返回ggplot对象供用户手动保存
# 创建日期：2025-08-01
# ==========================================
# 加载必要的包
library(ggplot2)
library(dplyr)
library(readr)
library(scales)
#' 绘制渐变色柱状图
#' 专为材料数据可视化设计的柱状图绘制函数
#'
#' @param materials 材料名称向量
#' @param values 对应的数值向量
#' @param gradient_low 渐变起始色（浅色，对应低数值）
#' @param gradient_high 渐变结束色（深色，对应高数值）
#' @param sort_by_value 是否按数值排序
#' @param y_limits Y轴范围
#' @param value_format 数值标签格式
#' @param x_label X轴标题
#' @param y_label Y轴标题
#' @return ggplot对象
plotBarChart <- function(materials,
values,
gradient_low = "#E3F2FD",
gradient_high = "#1976D2",
sort_by_value = TRUE,
y_limits = c(0, 1.2),
value_format = "%.2f",
x_label = "Substate Type",
y_label = "MVTR (g/(m²·24h))") {
# 创建数据框
plot_data <- data.frame(
Material = as.character(materials),
Value = as.numeric(values)
)
# 创建渐变主题图表
if (sort_by_value) {
p <- ggplot(plot_data, aes(x = reorder(Material, Value),
y = Value,
fill = Value))
} else {
p <- ggplot(plot_data, aes(x = Material,
y = Value,
fill = Value))
}
p <- p +
# 柱状图样式设置
geom_col(alpha = 0.9, width = 0.7) +
# 颜色设置：渐变色配置
scale_fill_gradient(low = gradient_low,
high = gradient_high,
guide = "none") +
# Y轴范围设置
scale_y_continuous(limits = y_limits,
expand = expansion(mult = c(0, 0.05))) +
# 数值标签设置：柱状图顶部的数字
geom_text(aes(label = sprintf(value_format, Value)),
vjust = -0.5,
size = 4,
fontface = "plain",
color = "black",
family = "Times New Roman") +
# 坐标轴标签设置
labs(x = x_label, y = y_label)
# 应用主题样式，与 plot_mvtr_bar.R 保持一致
p <- p +
theme_bw() +
theme(
# 背景颜色设置
plot.background = element_rect(fill = "white", color = NA),
panel.background = element_rect(fill = "white", color = NA),
# 字体设置：全局字体配置
text = element_text(family = "Times New Roman", color = "black"),
# 坐标轴文字设置
axis.text.x = element_text(angle = 0, hjust = 0.5, size = 11, color = "black"),
axis.text.y = element_text(size = 10, color = "black"),
# 坐标轴标题设置
axis.title = element_text(size = 12, face = "bold", color = "black"),
# 网格线设置
panel.grid = element_blank(),
# 完整边框设置：使用面板边框（四个边框等粗）
axis.line = element_blank(),
panel.border = element_rect(color = "black", fill = NA, linewidth = 0.6),
# 坐标轴刻度线设置
axis.ticks = element_line(color = "black", linewidth = 0.6),
axis.ticks.length = unit(-0.2, "cm"),  # 负值表示朝内
# 布局设置：图表边距
plot.margin = margin(10, 10, 0, 10)
)
return(p)
}
# MVTR 项目专用函数，保留原函数名
plot_mvtr_bar_advanced <- function(csv_file_path) {
# 读取数据
data <- read_csv(csv_file_path, locale = locale(encoding = "UTF-8"))
materials <- data[[1]]
values <- data[[ncol(data)]]
# 调用重构后的 plotBarChart
p <- plotBarChart(materials = materials,
values = values)
# 显示图表
print(p)
# 返回图表对象（可用于进一步修改或保存）
return(p)
}
# 加载必要的包
library(readr)
# 加载 plotBarChart 模块（假设在同一目录）
tryCatch({
source("plotBarChart.R")
}, error = function(e) {
cat("无法加载 plotBarChart.R，请确保文件在同一目录下\n")
cat("错误信息:", e$message, "\n")
stop("加载模块失败")
})
# 创建渐变主题柱状图
plot_mvtr_bar_advanced <- function(csv_file_path) {
# 读取数据
data <- read_csv(csv_file_path, locale = locale(encoding = "UTF-8"))
materials <- data[[1]]
values <- data[[ncol(data)]]
# 调用 plotBarChart 函数
p <- plotBarChart(materials = materials,
values = values)
# 显示图表
print(p)
# 返回图表对象（可用于进一步修改或保存）
return(p)
}
# ========== 使用示例 ==========
# 基本使用：读取CSV文件并绘制图表
csv_file <- "results.csv"
if (file.exists(csv_file)) {
cat("找到CSV文件，开始绘制图表...\n")
result <- plot_mvtr_bar_advanced(csv_file)
cat("图表绘制完成！\n")
} else {
cat("CSV文件不存在:", csv_file, "\n")
cat("当前工作目录:", getwd(), "\n")
cat("请确保 results.csv 文件在当前工作目录中\n")
}
# ========== 自定义参数说明 ==========
# 如需自定义样式，可以直接调用 plotBarChart 函数：
# plotBarChart(materials = c("Material A", "Material B"),
#              values = c(0.5, 1.2),
#              gradient_low = "#FFEBEE",     # 修改渐变起始色
#              gradient_high = "#D32F2F",    # 修改渐变结束色
#              y_limits = c(0, 2.0),         # 修改Y轴范围
#              value_format = "%.1f")        # 修改数值格式
# 加载必要的包
library(readr)
# 加载 plotBarChart 模块（假设在同一目录）
tryCatch({
source("plotBarChart.R")
}, error = function(e) {
cat("无法加载 plotBarChart.R，请确保文件在同一目录下\n")
cat("错误信息:", e$message, "\n")
stop("加载模块失败")
})
# 创建渐变主题柱状图
plot_mvtr_bar_advanced <- function(csv_file_path) {
# 读取数据
data <- read_csv(csv_file_path, locale = locale(encoding = "UTF-8"))
materials <- data[[1]]
values <- data[[ncol(data)]]
# 调用 plotBarChart 函数
p <- plotBarChart(materials = materials,
values = values)
# 显示图表
print(p)
# 返回图表对象（可用于进一步修改或保存）
return(p)
}
# ========== 使用示例 ==========
# 基本使用：读取CSV文件并绘制图表
csv_file <- "results.csv"
if (file.exists(csv_file)) {
cat("找到CSV文件，开始绘制图表...\n")
result <- plot_mvtr_bar_advanced(csv_file)
cat("图表绘制完成！\n")
} else {
cat("CSV文件不存在:", csv_file, "\n")
cat("当前工作目录:", getwd(), "\n")
cat("请确保 results.csv 文件在当前工作目录中\n")
}
# ========== 自定义参数说明 ==========
# 如需自定义样式，可以直接调用 plotBarChart 函数：
# plotBarChart(materials = c("Material A", "Material B"),
#              values = c(0.5, 1.2),
#              gradient_low = "#FFEBEE",     # 修改渐变起始色
#              gradient_high = "#D32F2F",    # 修改渐变结束色
#              y_limits = c(0, 2.0),         # 修改Y轴范围
#              value_format = "%.1f")        # 修改数值格式

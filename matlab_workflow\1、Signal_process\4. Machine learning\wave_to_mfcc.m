%% 肠鸣音深度学习分类系统
% 功能描述：基于CNN的肠鸣音类型自动识别与分类系统
%
% 输入要求：
% - 数据格式：.mat文件，包含肠鸣音信号数据和标签
% - 标签类型：SB(单次爆发)、MB(多次爆发)、CRS(连续随机)
% - 数据路径：全部数据文件夹，按时间段和测量次数组织
%
% 输出结果：
% - 训练好的CNN模型（保存至net文件夹）
% - 性能评估报告（准确率、精确率、召回率、F1分数）
% - 可视化图表（混淆矩阵、ROC曲线、性能分布图）
%
% 处理步骤：
% 1. 数据预处理：整合多时段数据并提取标签
% 2. 特征提取：使用Bark频谱提取音频特征
% 3. K折交叉验证：确保模型泛化能力
% 4. CNN训练：构建并训练深度学习模型
% 5. 性能评估：计算多维度评估指标
%
% 应用场景：医学音频信号分析、肠道健康监测、临床辅助诊断
%
% 作者：[作者信息]
% 日期：[创建日期]


clear all
clc
close all

%% 数据准备
% 自定义排序函数
function sorted_folders = sort_data_folders(folders)
    % 提取文件夹名中的数字
    numbers = zeros(length(folders), 1);
    for i = 1:length(folders)
        % 使用正则表达式提取数字
        name = folders(i).name;
        match = regexp(name, 'data(\d+)_', 'tokens');
        if ~isempty(match)
            numbers(i) = str2double(match{1}{1});
        end
    end
    
    % 根据提取的数字排序
    [~, idx] = sort(numbers);
    sorted_folders = folders(idx);
end

% 定义根目录
root_dir = '全部数据';

% 定义时间段（上午/下午）
time_periods = {'上午', '下午'};
time_periods_field = {'morning', 'afternoon'}; % 用于结构体字段名

% 定义测量次数
measurements = {'第一次', '第二次'};
measurements_field = {'first', 'second'}; % 用于结构体字段名

% 初始化数据存储和标签
combinedDataFolder = fullfile(pwd, 'BS_data_combined');

% 如果文件夹存在，清空文件夹内容，确保没有遗留的文件
if exist(combinedDataFolder, 'dir')
    delete(fullfile(combinedDataFolder, '*.mat'));
else
    mkdir(combinedDataFolder);
end

% 初始化文件计数器
fileCounter = 1;

% 遍历上午和下午
for period_idx = 1:length(time_periods)
    period = time_periods{period_idx};
    period_field = time_periods_field{period_idx};
    
    % 遍历第一次和第二次
    for meas_idx = 1:length(measurements)
        meas = measurements{meas_idx};
        meas_field = measurements_field{meas_idx};
        
        % 获取当前处理路径
        current_path = fullfile(root_dir, period, meas);
        
        % 获取所有5分钟数据文件夹并排序
        data_folders = dir(fullfile(current_path, 'data*_5min_tt'));
        data_folders = sort_data_folders(data_folders);
        
        % 遍历每个5分钟文件夹
        for folder_idx = 1:length(data_folders)
            folder_name = data_folders(folder_idx).name;
            folder_path = fullfile(current_path, folder_name);
            
            % 获取文件夹中的所有.mat文件
            mat_files = dir(fullfile(folder_path, '*.mat'));
            
            % 处理每个.mat文件
            for file_idx = 1:length(mat_files)
                file_name = mat_files(file_idx).name;
                file_path = fullfile(folder_path, file_name);
                
                % 加载MAT文件
                matData = load(file_path);
                
                % 提取文件名中的标签 (SB, MB, CRS)
                if contains(file_name, '_SB_')
                    label = 'SB';
                elseif contains(file_name, '_MB_')
                    label = 'MB';
                elseif contains(file_name, '_CRS_')
                    label = 'CRS';
                else
                    disp('No valid label found for this file. Skipping...');
                    continue;
                end
                
                % 提取音频数据
                fields = fieldnames(matData);
                tableData = matData.(fields{1});
                if istimetable(tableData) || istable(tableData)
                    audioData = tableData.column2; % 提取 column2 列
                else
                    error('MAT 文件数据格式不支持，期望为表格或时间表');
                end
                
                % 保存到新格式
                save(fullfile(combinedDataFolder, sprintf('sample_%03d_%s.mat', fileCounter, label)), 'audioData', 'label');
                
                % 显示进度
                fprintf('处理文件: %s, 标签: %s, 计数: %d\n', file_name, label, fileCounter);
                
                % 增加计数器
                fileCounter = fileCounter + 1;
            end
        end
    end
end
 
%% K折交叉验证数据准备
k = 5; % 设置折数
commands = categorical(["SB", "MB", "CRS"]);

% 创建数据存储对象
fds = fileDatastore(combinedDataFolder, ...
    'IncludeSubfolders', true, ...
    'FileExtensions', '.mat', ...
    'ReadFcn', @loadMatFileLabel);

% 读取所有标签
labels = [];
reset(fds);
while hasdata(fds)
    dataOut = read(fds);
    labels = [labels; categorical({dataOut.label})];
end

% 创建交叉验证分区
cvp = cvpartition(labels, 'KFold', k);

%% 定义标签类别
commands = categorical(["SB", "MB", "CRS"]);
% 确认所有标签都属于定义的类别
isCommand = ismember(labels, commands);
if all(isCommand)
    % 清理可能存在的未使用类别
    labels = removecats(labels);
else
    warning('存在未预期的标签类别，请检查数据集');
    disp('未预期的标签：');
    disp(unique(labels(~isCommand)));
end

%% 可视化K折交叉验证的数据分布
figure('Units', 'normalized', 'Position', [0.1, 0.1, 0.8, 0.8]);
tiledlayout(k, 2, 'TileSpacing', 'compact', 'Padding', 'compact');

% 遍历每一折
for fold = 1:k
    % 获取当前折的训练和验证索引
    trainIdx = training(cvp, fold);
    validIdx = test(cvp, fold);

    % 获取当前折的训练和验证标签
    foldTrainLabels = labels(trainIdx);
    foldValidLabels = labels(validIdx);

    % 可视化训练数据分布
    nexttile((fold-1)*2 + 1)
    histogram(foldTrainLabels)
    title(sprintf('Fold %d - Training Distribution', fold))
    ylabel("Number of Samples")
    xlabel("Labels")
    grid on

    % 可视化验证数据分布
    nexttile(fold*2)
    histogram(foldValidLabels)
    title(sprintf('Fold %d - Validation Distribution', fold))
    ylabel("Number of Samples")
    xlabel("Labels")
    grid on
end

% 添加总标题
sgtitle('K-Fold Cross Validation Data Distribution', 'FontSize', 14)

pause(2);% 延迟两秒


%% 准备训练数据，使用并行池
if canUseParallelPool
    useParallel = true;
    gcp;
else
    useParallel = false;
end

%% 音频参数设置
% 设置音频和特征提取参数
fs = 2570; % 采样率
segmentDuration = 1.5; % 每个音频片段的时长，单位为秒
frameDuration = 0.025; % 每帧的时长，单位为秒
hopDuration = 0.010; % 帧间隔时间
FFTLength = 512; % FFT长度
numBands = 50; % 频带数量

% 计算样本数
segmentSamples = round(segmentDuration * fs);
frameSamples = round(frameDuration * fs);
hopSamples = round(hopDuration * fs);
overlapSamples = frameSamples - hopSamples;

% 创建音频特征提取器
afe = audioFeatureExtractor( ...
    SampleRate=fs, ...
    FFTLength=FFTLength, ...
    Window=hann(frameSamples, "periodic"), ...
    OverlapLength=overlapSamples, ...
    barkSpectrum=true);
setExtractorParameters(afe, "barkSpectrum", NumBands=numBands, WindowNormalization=false);

%% K折交叉验证训练循环
% 存储每折的性能指标
foldPerformance = struct('TrainError', zeros(1,k), 'ValidationError', zeros(1,k));

for fold = 1:k
    fprintf('\nProcessing fold %d of %d\n', fold, k);

    % 获取当前折的训练和验证索引
    trainIdx = training(cvp, fold);
    validIdx = test(cvp, fold);

    % 创建当前折的训练和验证数据存储
    fdsTrain = subset(fds, trainIdx);
    fdsValidation = subset(fds, validIdx);

    % 获取当前折的标签
    labelsTrain = labels(trainIdx);
    labelsValidation = labels(validIdx);

    % 特征提取 - 训练数据
    XTrain = {};
    reset(fdsTrain);
    expectedFeatureSize = [];

    while hasdata(fdsTrain)
        data = read(fdsTrain);
        audio = data.audio;

        % 零填充
        paddedAudio = [zeros(floor((segmentSamples - length(audio)) / 2), 1); ...
                       audio; ...
                       zeros(ceil((segmentSamples - length(audio)) / 2), 1)];

        % 提取特征
        features = extract(afe, paddedAudio);
        logFeatures = log10(features + 1e-6);

        % 确保特征矩阵大小一致
        if isempty(expectedFeatureSize)
            expectedFeatureSize = size(logFeatures);
        else
            currentSize = size(logFeatures);
            if ~isequal(currentSize, expectedFeatureSize)
                if currentSize(1) > expectedFeatureSize(1)
                    logFeatures = logFeatures(1:expectedFeatureSize(1), :, :);
                elseif currentSize(1) < expectedFeatureSize(1)
                    logFeatures = [logFeatures; zeros(expectedFeatureSize(1) - currentSize(1), numBands)];
                end
            end
        end

        XTrain{end+1} = logFeatures;
    end
    XTrain = cat(4, XTrain{:});

    % 特征提取 - 验证数据
    XValidation = {};
    reset(fdsValidation);

    while hasdata(fdsValidation)
        data = read(fdsValidation);
        audio = data.audio;

        paddedAudio = [zeros(floor((segmentSamples - length(audio)) / 2), 1); ...
                       audio; ...
                       zeros(ceil((segmentSamples - length(audio)) / 2), 1)];

        features = extract(afe, paddedAudio);
        logFeatures = log10(features + 1e-6);

        currentSize = size(logFeatures);
        if ~isequal(currentSize, expectedFeatureSize)
            if currentSize(1) > expectedFeatureSize(1)
                logFeatures = logFeatures(1:expectedFeatureSize(1), :, :);
            elseif currentSize(1) < expectedFeatureSize(1)
                logFeatures = [logFeatures; zeros(expectedFeatureSize(1) - currentSize(1), numBands)];
            end
        end

        XValidation{end+1} = logFeatures;
    end
    XValidation = cat(4, XValidation{:});

    %% 可视化数据
    % 随机选择三个样本，展示波形和特征图
    specMin = min(XTrain, [], "all");% 特征最小值
    specMax = max(XTrain, [], "all");% 特征最大值
    idx = randperm(numel(labelsTrain), 3); % 随机选择三个训练样本的索引
    figure('Units', 'normalized', 'Position', [0.2, 0.2, 0.6, 0.6]);
    tlh = tiledlayout(2, 3);
    reset(fdsTrain); % 确保指针在文件列表的开头

    % 使用 subset 直接访问目标样本
    for ii = 1:3
        subsetFds = subset(fdsTrain, idx(ii));
        currentData = read(subsetFds);
        % 确保读取到了数据
        if isempty(currentData)
            error('未能成功读取训练数据中的音频样本');
        end
        % 可视化音频波形
        x = currentData.audio;
        nexttile(tlh, ii);
        plot(x);
        axis tight;
        title(string(labelsTrain(idx(ii))));
        % 可视化特征图
        nexttile(tlh, ii + 3);
        spect = XTrain(:, :, 1, idx(ii))'; % 提取对应的频谱特征
        pcolor(spect);
        shading flat;
        % 播放音频
        sound(x, fs);
        pause(2); % 播放结束后的间隔
    end


    %% 网络架构定义
    % 构建卷积神经网络模型
    numHops = size(XTrain, 1);
    timePoolSize = ceil(numHops / 8);
    dropoutProb = 0.2;
    numF = 12;
    numClasses = numel(categories(labels));
    % 定义网络层
    layers = [
    imageInputLayer([numHops, numBands, 1], Name="input")
    convolution2dLayer(3, numF, Padding="same", Name="conv_1")
    batchNormalizationLayer(Name="batchnorm_1")
    reluLayer(Name="relu_1")
    maxPooling2dLayer(3, Stride=2, Padding="same", Name="maxpool_1")

    convolution2dLayer(3, 2 * numF, Padding="same", Name="conv_2")
    batchNormalizationLayer(Name="batchnorm_2")
    reluLayer(Name="relu_2")
    maxPooling2dLayer(3, Stride=2, Padding="same", Name="maxpool_2")

    convolution2dLayer(3, 4 * numF, Padding="same", Name="conv_3")
    batchNormalizationLayer(Name="batchnorm_3")
    reluLayer(Name="relu_3")
    maxPooling2dLayer(3, Stride=2, Padding="same", Name="maxpool_3")

    convolution2dLayer(3, 4 * numF, Padding="same", Name="conv_4")
    batchNormalizationLayer(Name="batchnorm_4")
    reluLayer(Name="relu_4")

    convolution2dLayer(3, 4 * numF, Padding="same", Name="conv_5")
    batchNormalizationLayer(Name="batchnorm_5")
    reluLayer(Name="relu_5")
    maxPooling2dLayer([timePoolSize, 1], Name="time_pool")
    dropoutLayer(dropoutProb, Name="dropout")

    fullyConnectedLayer(numClasses, Name="fc")
    softmaxLayer(Name="softmax")
    classificationLayer(Name="output")];

    %% 训练选项
    miniBatchSize = 128; % 小批量训练样本数
    validationFrequency = floor(numel(labelsTrain) / miniBatchSize); % 验证频率

    % 定义训练选项
    options = trainingOptions("adam", ...
        InitialLearnRate=3e-4, ... % 初始学习率
        MaxEpochs=30, ... % 最大训练轮数
        MiniBatchSize=miniBatchSize, ...
        Shuffle="every-epoch", ... % 每轮训练后打乱数据
        Plots="training-progress", ... % 显示训练进度
        Verbose=false, ...
        ValidationData={XValidation, labelsValidation}, ... % 验证数据
        ValidationFrequency=validationFrequency, ...
        LearnRateSchedule="piecewise", ... % 学习率调整策略
        LearnRateDropFactor=0.1, ...
        LearnRateDropPeriod=10);

    %% 训练网络
    trainedNet = trainNetwork(XTrain, labelsTrain, layers, options); % 训练模型

    %% 评估性能
    YValidation = classify(trainedNet, XValidation); % 使用验证数据分类
    YTrain = classify(trainedNet, XTrain); % 使用训练数据分类

    % 计算训练和验证错误率
    foldPerformance.TrainError(fold) = mean(YTrain ~= labelsTrain);
    foldPerformance.ValidationError(fold) = mean(YValidation ~= labelsValidation);

    % 显示当前折的性能
    fprintf('Fold %d Results:\n', fold);
    fprintf('Training Error: %.2f%%\n', foldPerformance.TrainError(fold)*100);
    fprintf('Validation Error: %.2f%%\n', foldPerformance.ValidationError(fold)*100);

    % 绘制混淆矩阵
    figure('Name', sprintf('Fold %d Confusion Matrix', fold));
    cm = confusionchart(labelsValidation, YValidation, ...
        'Title', sprintf("Confusion Matrix for Fold %d", fold), ...
        'ColumnSummary', "column-normalized", ...
        'RowSummary', "row-normalized");


    pause(2); % 延迟一秒


    classNames = categories(labelsTrain);  % 获取类别名称
    [accuracy, precision, recall, f1, confMat] = calculateMetrics(labelsValidation, YValidation, classNames);

    % 保存每折的性能指标
    foldPerformance.Accuracy(fold) = accuracy;
    foldPerformance.Precision(fold, :) = precision;
    foldPerformance.Recall(fold, :) = recall;
    foldPerformance.F1(fold, :) = f1;

    % 显示当前折的详细性能指标
    fprintf('\nFold %d Detailed Results:\n', fold);
    fprintf('Accuracy: %.2f%%\n', accuracy*100);
    fprintf('\nPer-class metrics:\n');
    for i = 1:length(classNames)
        fprintf('Class %s:\n', char(classNames(i)));  % 修改这里使用char转换
        fprintf('  Precision: %.2f%%\n', precision(i)*100);
        fprintf('  Recall: %.2f%%\n', recall(i)*100);
        fprintf('  F1 Score: %.2f%%\n', f1(i)*100);
    end


%**************************************************************
    % 保存当前折的模型（可选）
    save(sprintf('trainedNet_fold_%d.mat', fold), 'trainedNet');

    % 指定要保存训练好的网络的文件夹
    netFolder = fullfile(pwd, 'net');

    % 如果文件夹不存在,则创建���
    if ~exist(netFolder, 'dir')
        mkdir(netFolder);
    end

    % 获取当前时间戳
    timestamp = datestr(now, 'mmddHHMM');

    % 生成带时间戳的文件名
    netFileName = sprintf('trainedNet_%s.mat', timestamp);

    % 保存训练好的网络
    save(fullfile(netFolder, netFileName), 'trainedNet');

%**************************************************************

end

    %% 在k-fold循环结束后添加总体性能评估
    fprintf('\nOverall Performance:\n');
    fprintf('Average Training Error: %.2f%% (±%.2f%%)\n', ...
        mean(foldPerformance.TrainError)*100, ...
        std(foldPerformance.TrainError)*100);
    fprintf('Average Validation Error: %.2f%% (±%.2f%%)\n', ...
        mean(foldPerformance.ValidationError)*100, ...
        std(foldPerformance.ValidationError)*100);

    % 绘制K折交叉验证性能图
    figure('Name', 'K-fold Cross Validation Performance');
    bar([foldPerformance.TrainError' foldPerformance.ValidationError']*100);
    xlabel('Fold');
    ylabel('Error Rate (%)');
    title('K-fold Cross Validation Performance');
    legend('Training Error', 'Validation Error');
    grid on;


    % 在k-fold循环结束后添加总体性能评估
    fprintf('\nOverall Performance Metrics:\n');
    fprintf('Average Accuracy: %.2f%% (±%.2f%%)\n', ...
        mean(foldPerformance.Accuracy)*100, ...
        std(foldPerformance.Accuracy)*100);

    % 计算和显示每个类别的平均性能指标
    avgPrecision = mean(foldPerformance.Precision);
    avgRecall = mean(foldPerformance.Recall);
    avgF1 = mean(foldPerformance.F1);
    stdPrecision = std(foldPerformance.Precision);
    stdRecall = std(foldPerformance.Recall);
    stdF1 = std(foldPerformance.F1);

    fprintf('\nPer-class Average Metrics:\n');
    for i = 1:length(classNames)
        fprintf('\nClass %s:\n', char(classNames(i)));
        fprintf('  Precision: %.2f%% (±%.2f%%)\n', ...
            avgPrecision(i)*100, stdPrecision(i)*100);
        fprintf('  Recall: %.2f%% (±%.2f%%)\n', ...
            avgRecall(i)*100, stdRecall(i)*100);
        fprintf('  F1 Score: %.2f%% (±%.2f%%)\n', ...
            avgF1(i)*100, stdF1(i)*100);
    end

    % 绘制所有指标的箱型图
    figure('Name', 'Performance Metrics Distribution');
    metrics = [foldPerformance.Accuracy', ...
              reshape(foldPerformance.Precision, [], size(foldPerformance.Precision, 2)), ...
              reshape(foldPerformance.Recall, [], size(foldPerformance.Recall, 2)), ...
              reshape(foldPerformance.F1, [], size(foldPerformance.F1, 2))];

    % 创建标签（修改这部分以修复错误）
    boxLabels = cell(1, 1 + 3*length(classNames));
    boxLabels{1} = 'Accuracy';
    idx = 2;
    for metric = {'Precision', 'Recall', 'F1'}
        for i = 1:length(classNames)
            boxLabels{idx} = sprintf('%s-%s', metric{1}, char(classNames(i)));
            idx = idx + 1;
        end
    end

    % 绘制箱型图
    boxplot(metrics*100, 'Labels', boxLabels);
    ylabel('Percentage (%)');
    title('Distribution of Performance Metrics Across Folds');
    xtickangle(45); % 倾斜标签以防重叠
    grid on;

    % 保存所有评估结果
    results = struct();
    results.foldPerformance = foldPerformance;
    results.avgPrecision = avgPrecision;
    results.avgRecall = avgRecall;
    results.avgF1 = avgF1;
    results.stdPrecision = stdPrecision;
    results.stdRecall = stdRecall;
    results.stdF1 = stdF1;
    save('evaluation_results.mat', 'results');






%*****************************
% 辅助函数定义
function dataOut = loadMatFileLabel(filename)
    matData = load(filename);
    if isfield(matData, 'audioData') && isfield(matData, 'label')
        dataOut.audio = matData.audioData;
        dataOut.label = matData.label;
    else
        error('MAT 文件缺少 audioData 或 label 字段');
    end
end




% 在原有代码基础上，添加以下评估函数
function [accuracy, precision, recall, f1, confMat] = calculateMetrics(actual, predicted, classes)
    % 计算混淆矩阵
    confMat = zeros(length(classes));
    for i = 1:length(actual)
        actualIdx = find(classes == actual(i));
        predictedIdx = find(classes == predicted(i));
        confMat(actualIdx, predictedIdx) = confMat(actualIdx, predictedIdx) + 1;
    end

    % 计算每���类别的指标
    precision = zeros(length(classes), 1);
    recall = zeros(length(classes), 1);
    f1 = zeros(length(classes), 1);

    for i = 1:length(classes)
        % 真阳性
        tp = confMat(i,i);
        % 假阳性
        fp = sum(confMat(:,i)) - tp;
        % 假阴性
        fn = sum(confMat(i,:)) - tp;

        % 计算精确率
        if (tp + fp) == 0
            precision(i) = 0;
        else
            precision(i) = tp / (tp + fp);
        end

        % 计算召回率
        if (tp + fn) == 0
            recall(i) = 0;
        else
            recall(i) = tp / (tp + fn);
        end

        % 计算F1分数
        if (precision(i) + recall(i)) == 0
            f1(i) = 0;
        else
            f1(i) = 2 * (precision(i) * recall(i)) / (precision(i) + recall(i));
        end
    end

    % 计算总体准确率
    accuracy = sum(diag(confMat)) / sum(confMat(:));
end

function plotROCCurve(actual, predicted, classes)
    % 为每个类别计算ROC曲线
    figure('Name', 'ROC Curves');
    hold on;

    colors = lines(length(classes));
    auc_scores = zeros(length(classes), 1);

    for i = 1:length(classes)
        % 转换为二分类问题
        actual_binary = (actual == classes(i));
        scores = zeros(length(predicted), 1);
        for j = 1:length(predicted)
            if predicted(j) == classes(i)
                scores(j) = 1;
            end
        end

        % 计算ROC曲线点
        [X,Y,T,AUC] = perfcurve(actual_binary, scores, true);

        % 绘制ROC曲线
        plot(X, Y, 'Color', colors(i,:), 'LineWidth', 2);
        auc_scores(i) = AUC;
    end

    % 添加对角线
    plot([0 1], [0 1], 'k--');

    % 设置图形属性
    xlabel('False Positive Rate');
    ylabel('True Positive Rate');
    title('ROC Curves for Each Class');
    legend([cellstr(categories(classes)), 'Random'], 'Location', 'southeast');
    grid on;
    hold off;
end

function splitVideo(mode, inputFolder, outputFolder)
%SPLITVIDEO 通用视频分割函数
%   该函数根据指定的分割模式将输入文件夹中的视频文件分割成多个较短的片段。
%   支持四种不同的分割模式，每种模式都设计为总时长约300秒（5分钟）。
%   具有完整的错误处理、进度显示和用户交互功能。
%
%   语法:
%   splitVideo(mode)
%   splitVideo(mode, inputFolder, outputFolder)
%
%   输入参数:
%   mode        - 分割模式编号 (整数: 1-4)
%                 1: 9秒模式  - 33个9秒片段 + 1个3秒片段
%                 2: 18秒模式 - 16个18秒片段 + 1个12秒片段  
%                 3: 24秒模式 - 12个24秒片段 + 1个12秒片段
%                 4: 60秒模式 - 5个60秒片段
%   inputFolder - 输入视频文件夹路径 (字符串，可选)
%                 默认: 'matlab_workflow\1、Signal_process\1. Read files (audio segments, video segments)\video\1、Raw data'
%   outputFolder- 输出视频片段保存路径 (字符串，可选)
%                 默认: 'matlab_workflow\1、Signal_process\1. Read files (audio segments, video segments)\video\2、Processed data'
%
%   输出文件:
%   生成的视频片段按以下规则命名: data{组号}_{片段号}.mp4
%   - 组号: 从1开始的整数，表示处理周期
%   - 片段号: 从1开始，根据分割模式确定最大值
%
%   功能特性:
%   - 支持批量处理多个视频文件
%   - 交互式选择是否删除视频开头部分
%   - 实时显示处理进度和状态信息
%   - 完整的错误处理和异常恢复机制
%   - 保持原视频的帧率和质量设置
%   - 支持多种视频格式: .mp4, .avi, .mov
%
%   示例:
%   splitVideo(1);  % 使用9秒模式，默认路径
%   splitVideo(2, 'input', 'output');  % 使用18秒模式，自定义路径
%
%   注意事项:
%   - 确保有足够的磁盘空间存储分割后的视频
%   - 处理大文件时可能需要较长时间
%   - 建议在处理前备份原始视频文件
%   - 需要Computer Vision Toolbox支持
%
%   另请参阅: GETVIDEOSPLITCONFIG, VIDEOSPLITMAIN, VIDEOREADER, VIDEOWRITER

% 输入参数验证和默认值设置
if nargin < 1
    error('splitVideo:MissingInput', '必须提供分割模式参数');
end

% 设置默认路径
if nargin < 2 || isempty(inputFolder)
    % 获取函数所在目录
    functionDir = fileparts(mfilename('fullpath'));
    inputFolder = fullfile(functionDir, '1、Raw data');
end

if nargin < 3 || isempty(outputFolder)
    % 获取函数所在目录
    functionDir = fileparts(mfilename('fullpath'));
    outputFolder = fullfile(functionDir, '2、Processed data');
end

% 获取分割模式配置
try
    config = getVideoSplitConfig(mode);
catch ME
    error('splitVideo:ConfigError', '获取分割配置失败: %s', ME.message);
end

% 显示分割模式信息
fprintf('\n=== 视频分割处理 ===\n');
fprintf('分割模式: %s\n', config.modeName);
fprintf('配置描述: %s\n', config.description);
fprintf('总片段数: %d个\n', config.totalSegments);
fprintf('总时长: %d秒 (%.1f分钟)\n', config.totalDuration, config.totalDuration/60);
fprintf('==================\n\n');

% 确保输入输出文件夹存在
if ~exist(inputFolder, 'dir')
    error('splitVideo:InputFolderNotFound', '输入文件夹不存在: %s', inputFolder);
end

% 如果输出文件夹不存在，则创建
if ~exist(outputFolder, 'dir')
    mkdir(outputFolder);
    fprintf('已创建输出文件夹: %s\n', outputFolder);
end

% 获取输入文件夹中的所有视频文件
videoFiles = [];
for fmt = config.supportedInputFormats
    files = dir(fullfile(inputFolder, ['*' fmt{1}]));
    videoFiles = [videoFiles; files]; %#ok<AGROW>
end

if isempty(videoFiles)
    error('splitVideo:NoVideoFiles', ...
        '在输入文件夹中未找到视频文件\n支持的格式: %s', ...
        strjoin(config.supportedInputFormats, ', '));
end

fprintf('找到 %d 个视频文件待处理\n', length(videoFiles));

% 处理每个视频文件
for fileIdx = 1:length(videoFiles)
    currentVideo = videoFiles(fileIdx);
    fprintf('\n开始处理视频 %d/%d: %s\n', fileIdx, length(videoFiles), currentVideo.name);
    
    % 构建完整的输入文件路径
    inputVideoPath = fullfile(inputFolder, currentVideo.name);
    
    try
        % 创建视频读取对象
        videoReader = VideoReader(inputVideoPath);
        
        % 执行交互式预处理
        startTime = performInteractivePreprocessing(videoReader, currentVideo.name);
        
        % 重新定位视频读取起始点
        videoReader.CurrentTime = startTime;
        
        % 获取视频信息
        frameRate = videoReader.FrameRate;
        remainingDuration = videoReader.Duration - startTime;
        
        % 计算每种片段需要的帧数
        normalFramesPerSegment = round(config.normalDuration * frameRate);
        shortFramesPerSegment = round(config.shortDuration * frameRate);
        
        % 初始化计数器
        groupIndex = 1;  % 组索引
        segmentInGroupIndex = 1;  % 组内片段索引
        
        % 处理视频分割
        processVideoSegments(videoReader, config, outputFolder, groupIndex, ...
            segmentInGroupIndex, normalFramesPerSegment, shortFramesPerSegment, frameRate);
        
        % 显示当前视频的处理统计信息
        displayVideoProcessingStats(currentVideo.name, videoReader.Duration, startTime, remainingDuration);
        
    catch ME
        fprintf('\n处理视频 %s 时出错:\n%s\n', currentVideo.name, ME.message);
        continue; % 继续处理下一个视频
    end
end

fprintf('\n所有视频处理完成！\n');
end

function processVideoSegments(videoReader, config, outputFolder, groupIndex, ...
    segmentInGroupIndex, normalFramesPerSegment, shortFramesPerSegment, frameRate)
%PROCESSVIDEOSEGMENTS 处理视频分割的核心函数
%   该函数执行实际的视频分割操作，根据配置参数将视频分割成指定的片段

while hasFrame(videoReader)
    % 确定当前片段的帧数和时长
    if config.shortCount == 0
        % 模式4：只有常规片段
        currentSegmentFrames = normalFramesPerSegment;
        segmentDuration = config.normalDuration;
    else
        % 其他模式：有常规片段和短片段
        if segmentInGroupIndex <= config.normalCount
            currentSegmentFrames = normalFramesPerSegment;
            segmentDuration = config.normalDuration;
        else
            currentSegmentFrames = shortFramesPerSegment;
            segmentDuration = config.shortDuration;
        end
    end

    % 构建输出文件名
    outputFileName = fullfile(outputFolder, sprintf('data%d_%d.mp4', groupIndex, segmentInGroupIndex));

    % 创建视频写入对象
    videoWriter = VideoWriter(outputFileName, config.videoFormat);
    videoWriter.FrameRate = frameRate;
    videoWriter.Quality = config.videoQuality;
    open(videoWriter);

    % 读取并写入当前片段的所有帧
    framesWritten = 0;
    while framesWritten < currentSegmentFrames && hasFrame(videoReader)
        frame = readFrame(videoReader);
        writeVideo(videoWriter, frame);
        framesWritten = framesWritten + 1;

        % 显示进度
        if mod(framesWritten, 30) == 0
            fprintf('\r组 %d, 片段 %d 进度: %.1f%%', groupIndex, segmentInGroupIndex, ...
                (framesWritten / currentSegmentFrames) * 100);
        end
    end

    % 关闭视频写入对象
    close(videoWriter);
    fprintf('\n完成片段 data%d_%d (%.1f秒)\n', groupIndex, segmentInGroupIndex, segmentDuration);

    % 更新计数器
    if segmentInGroupIndex == config.totalSegments
        groupIndex = groupIndex + 1;
        segmentInGroupIndex = 1;
    else
        segmentInGroupIndex = segmentInGroupIndex + 1;
    end

    % 如果剩余帧数不足以构成一个完整片段，则退出
    if videoReader.CurrentTime >= videoReader.Duration
        break;
    end
end

end

function startTime = performInteractivePreprocessing(videoReader, videoFileName)
%PERFORMINTERACTIVEPREPROCESSING 执行交互式视频预处理
%   该函数为每个视频文件提供交互式预处理选项，允许用户选择是否删除
%   视频开头部分，并提供详细的用户体验优化和错误处理。
%
%   输入参数:
%   videoReader   - VideoReader对象，用于获取视频信息
%   videoFileName - 视频文件名，用于显示信息
%
%   输出参数:
%   startTime     - 处理起始时间（秒），0表示从开头开始

fprintf('\n=== 视频预处理设置 ===\n');
fprintf('当前视频: %s\n', videoFileName);
fprintf('视频总时长: %.2f秒 (%.1f分钟)\n', videoReader.Duration, videoReader.Duration/60);

% 显示时间参考信息
fprintf('\n时间参考:\n');
fprintf('  0秒 = 视频开头\n');
fprintf('  %.0f秒 = 视频中点\n', videoReader.Duration/2);
fprintf('  %.2f秒 = 视频结尾\n', videoReader.Duration);

% 询问用户是否需要删除开头部分
fprintf('\n💡 提示: 如果视频开头有不需要的内容（如标题、广告等），可以选择删除\n');
while true
    answer = input('是否需要删除视频开头部分？(y/n): ', 's');

    % 验证输入
    if strcmpi(answer, 'y') || strcmpi(answer, 'yes')
        startTime = getValidStartTime(videoReader);
        break;
    elseif strcmpi(answer, 'n') || strcmpi(answer, 'no')
        startTime = 0;
        fprintf('✅ 将从视频开头（第0秒）开始处理\n');
        break;
    else
        fprintf('❌ 请输入 y（是）或 n（否）\n');
    end
end

fprintf('========================\n');

end

function startTime = getValidStartTime(videoReader)
%GETVALIDSTARTTIME 获取有效的起始时间
%   通过用户输入获取有效的视频处理起始时间，包含完整的输入验证

maxDuration = videoReader.Duration;

fprintf('\n📝 请输入要删除的时长（从视频开头算起）:\n');
fprintf('   - 输入范围: 0 到 %.2f 秒\n', maxDuration - 1);
fprintf('   - 建议: 先预览视频确定需要删除的时长\n');

while true
    try
        % 获取用户输入
        userInput = input('请输入删除时长（秒）: ');

        % 验证输入是否为数字
        if ~isnumeric(userInput) || ~isscalar(userInput)
            fprintf('❌ 请输入一个数字\n');
            continue;
        end

        % 验证输入范围
        if userInput < 0
            fprintf('❌ 删除时长不能为负数，请重新输入\n');
            continue;
        end

        if userInput >= maxDuration
            fprintf('❌ 删除时长不能大于等于视频总时长（%.2f秒），请重新输入\n', maxDuration);
            continue;
        end

        % 输入有效，设置起始时间
        startTime = userInput;

        % 显示确认信息
        fprintf('✅ 将从第%.2f秒开始处理视频\n', startTime);
        fprintf('   删除时长: %.2f秒\n', startTime);
        fprintf('   剩余时长: %.2f秒 (%.1f分钟)\n', ...
            maxDuration - startTime, (maxDuration - startTime)/60);

        % 最终确认
        confirm = input('确认以上设置？(y/n): ', 's');
        if strcmpi(confirm, 'y') || strcmpi(confirm, 'yes')
            break;
        else
            fprintf('请重新输入删除时长:\n');
        end

    catch ME
        fprintf('❌ 输入错误: %s\n', ME.message);
        fprintf('请输入一个有效的数字\n');
    end
end

end

function displayVideoProcessingStats(videoFileName, originalDuration, startTime, processedDuration)
%DISPLAYVIDEOPROCESSINGSTATS 显示视频处理统计信息
%   显示详细的视频处理统计信息，包括原始时长、删除时长和实际处理时长

fprintf('\n=== 视频处理完成 ===\n');
fprintf('📹 视频文件: %s\n', videoFileName);
fprintf('📊 处理统计:\n');
fprintf('   原始总时长: %.2f秒 (%.1f分钟)\n', originalDuration, originalDuration/60);

if startTime > 0
    fprintf('   删除开头时长: %.2f秒 (%.1f分钟)\n', startTime, startTime/60);
    fprintf('   实际处理时长: %.2f秒 (%.1f分钟)\n', processedDuration, processedDuration/60);
    fprintf('   处理比例: %.1f%% (删除了%.1f%%)\n', ...
        (processedDuration/originalDuration)*100, (startTime/originalDuration)*100);
else
    fprintf('   删除开头时长: 无\n');
    fprintf('   实际处理时长: %.2f秒 (%.1f分钟)\n', processedDuration, processedDuration/60);
    fprintf('   处理比例: 100%% (完整处理)\n');
end

fprintf('✅ 处理状态: 成功完成\n');
fprintf('======================\n');

end

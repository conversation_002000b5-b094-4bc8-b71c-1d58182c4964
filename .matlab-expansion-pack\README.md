# MATLAB Expansion Pack

Comprehensive MATLAB development toolkit with specialized AI agents for programming, algorithm design, simulation modeling, and data analysis. **Enhanced with dev-agent inspired architecture for superior code quality and workflow management.**

## 🚀 Dev-Agent Inspired Enhancements

This expansion pack now incorporates proven dev-agent principles for professional MATLAB development:

### 🔧 Configuration-Driven Architecture
- **config.yml**: MATLAB-specific configuration with toolbox management and performance benchmarks
- **Environment Detection**: Automatic MATLAB installation and toolbox verification
- **Quality Gates**: Automated syntax, performance, and test coverage validation

### 📋 Sequential Task Execution
- **Structured Workflow**: Read task→Implement→Test→Validate→Update [x]→Next task
- **Quality Gate Discipline**: NEVER complete tasks with failing validations
- **Debug Log Discipline**: Comprehensive logging with revert tracking

### 🎯 MATLAB-Centric Principles
- **Performance-First**: Vectorization analysis and optimization recommendations
- **Toolbox Awareness**: Dependency checking with alternative suggestions
- **Testing Integration**: matlab.unittest framework with coverage requirements

## Meet Your MATLAB Development Team

### 🎯 Dr. <PERSON> - <PERSON><PERSON>AB Technical Lead (Orchestrator)

*Senior MATLAB Technical Lead with PhD in Engineering and 15+ years MATLAB/Simulink experience*

Dr. <PERSON> is your MATLAB project coordinator who will guide you through the complete development process using numbered options and structured workflows. <PERSON> specializes in project coordination, team management, and technical decision-making across all MATLAB development domains.

### 💼 Specialist Agents

- **💻 Sarah Rodriguez** - MATLAB Programming Specialist *(Enhanced with Dev-Agent Architecture)*
  - **Dev-Agent Workflow**: Sequential task execution with quality gates and comprehensive validation
  - **Performance Analysis**: Integrated profiling, vectorization analysis, and optimization recommendations
  - **Testing Framework**: matlab.unittest integration with automated coverage tracking
  - **Toolbox Management**: Dependency verification and alternative suggestions
  - 10+ years experience in scientific computing and engineering applications

- **🧮 Dr. Michael Zhang** - Algorithm Development Expert  
  - PhD in Applied Mathematics with 15+ years algorithm development experience
  - Specializes in mathematical modeling, numerical methods, and optimization
  - Expert in bridging theory and practical implementation

- **🔧 Jennifer Park** - Simulink Modeling Specialist
  - MS in Control Systems Engineering with 12+ years Simulink experience
  - Expert in system modeling, simulation design, and model-based development
  - Specializes in multi-domain systems and real-time simulation

- **📊 David Kumar** - MATLAB Data Analysis Expert
  - PhD in Statistics with 8+ years data analysis experience
  - Expert in statistical modeling, visualization, and machine learning
  - Passionate about extracting actionable insights from data

- **📊 Dr. Elena Vasquez** - MATLAB Visualization & Graphics Expert *(Enhanced with Dev-Agent Quality Standards)*
  - **Dev-Agent Workflow**: Sequential plotting tasks with accessibility validation and quality gates
  - **Quality-Driven Design**: Publication-quality figures with comprehensive validation checks
  - **Export Management**: Multi-format export with automated quality verification
  - **Accessibility Discipline**: Colorblind-friendly design validation and performance optimization
  - PhD in Information Visualization with 12+ years graphics experience

## Quick Start

### 1. Prepare Data Files (place in `bmad-core/data/`)

```markdown
# Required Files:
- matlab-coding-standards.md    # Your organization's coding guidelines
- project-requirements.md       # Current project specifications  
- performance-benchmarks.md     # Performance targets and criteria
```

### 2. Launch MATLAB Orchestrator

```bash
npm run agent matlab-orchestrator
```

### 3. Follow Numbered Options

Dr. Alex Chen will present numbered choices for each decision:
- Project type selection (algorithm, simulation, data analysis, etc.)
- Team member assignment based on project needs
- Workflow coordination and milestone planning
- Quality assurance and review processes

### 4. Quality Assurance

Multi-level validation with star ratings ensures excellence:
- ⭐⭐⭐⭐⭐ Code quality assessment
- ⭐⭐⭐⭐⭐ Performance evaluation  
- ⭐⭐⭐⭐⭐ Documentation completeness
- ✅/❌ Ready/Not Ready quality gates

## Advanced Features

### 🤖 LLM Template System
- Intelligent document generation with conditional content
- Dynamic variable replacement and context awareness
- Progressive disclosure with guided completion
- Integration with quality assurance checklists

### 🔄 Workflow Orchestration  
- Decision trees for different project types
- Structured handoff protocols between agents
- Quality gates and milestone validation
- Automated progress tracking and reporting

### 👥 Character Consistency
- Persistent personas across all interactions
- Domain-appropriate communication styles
- Numbered options protocol for all decisions
- Professional authenticity and expertise

### ✅ Quality Integration
- Comprehensive validation at every development stage
- Multi-level assessment (Basic/Comprehensive/Expert)
- Star rating systems with improvement recommendations
- Ready/Not Ready frameworks with clear criteria

## Project Types Supported

### Algorithm Development
1. **Mathematical Modeling** → Dr. Michael Zhang
2. **Algorithm Design** → Dr. Michael Zhang  
3. **Implementation** → Sarah Rodriguez
4. **Performance Validation** → Team collaboration
5. **Optimization** → Sarah Rodriguez

### Data Analysis Projects
1. **Data Assessment** → David Kumar
2. **Analysis Design** → David Kumar
3. **Implementation** → David Kumar + Sarah Rodriguez
4. **Validation** → David Kumar
5. **Visualization & Reporting** → David Kumar

### Simulation Modeling
1. **System Analysis** → Jennifer Park
2. **Model Design** → Jennifer Park
3. **Implementation** → Jennifer Park + Sarah Rodriguez  
4. **Validation** → Jennifer Park
5. **Performance Optimization** → Sarah Rodriguez

### General MATLAB Development
1. **Requirements Analysis** → Dr. Alex Chen
2. **Architecture Design** → Dr. Alex Chen + Specialists
3. **Implementation** → Sarah Rodriguez
4. **Testing & Validation** → Team collaboration
5. **Documentation & Deployment** → Team collaboration

## Components Overview

### Agents (6)
- **matlab-orchestrator** - Project coordination and workflow management
- **matlab-programmer** - Code development and optimization
- **algorithm-designer** - Mathematical modeling and algorithm design
- **simulation-modeler** - Simulink modeling and system simulation
- **data-analyst** - Data analysis and statistical modeling
- **visualization-specialist** - Data visualization and graphics design

### Templates (6)
- **matlab-project-spec-tmpl** - Comprehensive project specification
- **algorithm-design-tmpl** - Algorithm design and documentation
- **simulink-model-spec-tmpl** - Simulink model specification
- **matlab-code-review-tmpl** - Code review documentation
- **data-analysis-report-tmpl** - Data analysis reporting
- **visualization-guide-tmpl** - Visualization design guide

### Quality Systems (5)
- **matlab-code-quality-checklist** - Multi-level code quality assessment
- **algorithm-validation-checklist** - Algorithm correctness validation
- **simulink-model-checklist** - Simulink model quality validation
- **data-analysis-checklist** - Statistical analysis validation
- **visualization-quality-checklist** - Visualization design and quality assessment

### Knowledge Base (6)
- **matlab-best-practices** - Programming standards and conventions
- **algorithm-design-patterns** - Common algorithmic implementations
- **simulink-modeling-guidelines** - System modeling best practices
- **matlab-optimization-techniques** - Performance optimization strategies
- **numerical-methods-reference** - Mathematical methods reference
- **matlab-plotting-best-practices** - Visualization and graphics best practices

## Workflow Example

```mermaid
flowchart LR
    A[Dr. Alex Chen<br/>Project Setup] --> B{Project Type?}
    B -->|Algorithm| C[Dr. Michael Zhang<br/>Algorithm Design]
    B -->|Simulation| D[Jennifer Park<br/>Model Design]  
    B -->|Data Analysis| E[David Kumar<br/>Analysis Design]
    C --> F[Sarah Rodriguez<br/>Implementation]
    D --> F
    E --> F
    F --> G[Team<br/>Testing & Validation]
    G --> H[Dr. Alex Chen<br/>Quality Review]
```

## Quality Standards

### Code Quality (⭐⭐⭐⭐⭐)
- MATLAB best practices adherence
- Vectorization and performance optimization
- Comprehensive documentation and comments
- Robust error handling and validation

### Documentation (⭐⭐⭐⭐⭐)  
- Clear function headers and help text
- Usage examples and demonstrations
- Architecture and design documentation
- Maintenance and deployment guides

### Testing (⭐⭐⭐⭐⭐)
- Comprehensive unit test coverage
- Integration and system testing
- Performance benchmarking and validation
- Edge case and robustness testing

## Integration with Core BMAD

This expansion pack integrates seamlessly with core BMAD agents:
- **dev** - For general programming support and integration
- **architect** - For system design and architecture planning
- **qa** - For quality assurance processes and validation

## Support and Maintenance

### Continuous Improvement
- Regular updates to best practices and guidelines
- New template additions based on user feedback
- Performance optimization and feature enhancements
- Integration with latest MATLAB/Simulink capabilities

### Community Contributions
- Open to community feedback and suggestions
- Collaborative development of new features
- Sharing of best practices and lessons learned
- Knowledge base expansion and refinement

---

**Ready to start your MATLAB development project?**

Launch the orchestrator and let Dr. Alex Chen guide you through a structured, quality-focused development process with expert specialist support at every stage!

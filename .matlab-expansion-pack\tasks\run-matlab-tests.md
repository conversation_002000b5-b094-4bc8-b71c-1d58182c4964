# run-matlab-tests

Execute comprehensive MATLAB testing including unit tests, performance benchmarks, and code quality validation.

## Purpose
Run all MATLAB tests and validations to ensure code quality, performance, and correctness before marking tasks complete.

## Inputs
- **testDirectory** (optional): Directory containing test files (default: tests/)
- **performanceTests** (optional): Whether to run performance benchmarks (default: true)
- **coverageThreshold** (optional): Minimum test coverage required (default: 80%)

## Process

### 1. Environment Check
- Verify MATLAB installation and required toolboxes
- Check test framework availability (matlab.unittest)
- Validate test directory structure

### 2. Unit Tests Execution
```matlab
% Run all unit tests
import matlab.unittest.TestSuite
import matlab.unittest.TestRunner
import matlab.unittest.plugins.CodeCoveragePlugin

% Create test suite
suite = TestSuite.fromFolder('tests');

% Create test runner with coverage
runner = TestRunner.withTextOutput;
runner.addPlugin(CodeCoveragePlugin.forFolder('.'));

% Run tests
results = runner.run(suite);
```

### 3. Performance Benchmarks
- Execute performance tests if enabled
- Compare against configured benchmarks
- Log execution times and memory usage

### 4. Code Quality Checks
- MATLAB Code Analyzer (mlint) validation
- Vectorization analysis
- Documentation completeness check

### 5. Results Reporting
- Generate test results summary
- Update matlabTestResults log
- Report coverage statistics
- Highlight any failures or performance issues

## Outputs
- **testResults**: Detailed test execution results
- **coverageReport**: Code coverage analysis
- **performanceMetrics**: Execution time and memory usage
- **qualityScore**: Overall code quality assessment

## Success Criteria
- All unit tests pass
- Code coverage meets threshold
- Performance benchmarks satisfied
- No critical code quality issues

## Error Handling
- Missing test files: Continue with available tests
- Toolbox unavailable: Skip dependent tests with warning
- Performance failure: Report but don't block unless critical
- Coverage below threshold: Warning for review

## Integration
- Updates matlabDebugLog with test execution details
- Integrates with task-execution flow validation
- Supports both automated and manual test execution

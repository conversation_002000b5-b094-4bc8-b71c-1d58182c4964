# ==========================================
# 文件名：plotBarChart.R
# 主要功能：绘制通用柱状图（可移植到其他项目）
# 依赖文件：无
# 主要函数：
#   - plotBarChart(): 绘制单组或双组数据对比柱状图
# 输入参数：一组或两组数值数据，组标签
# 输出结果：返回ggplot对象供用户手动保存
# 创建日期：2025-08-01
# ==========================================

# 加载必要的包
library(ggplot2)

# Windows系统字体设置
if (.Platform$OS.type == "windows") {
  windowsFonts(times = windowsFont("Times New Roman"))
}

# Windows系统字体设置
if (.Platform$OS.type == "windows") {
  windowsFonts(times = windowsFont("Times New Roman"))
}

#' 绘制通用柱状图
#' 可移植到其他项目使用的通用柱状图绘制函数
#'
#' @param group1_data 第一组数值数据
#' @param group2_data 第二组数值数据（可选，为NULL时只显示单组数据）
#' @param group1_label 第一组标签
#' @param group2_label 第二组标签（可选）
#' @return ggplot对象
plotBarChart <- function(group1_data = c(20.3319, 25.7963, 21.8242),
                        group2_data = NULL,
                        group1_label = "Group 1",
                        group2_label = "Group 2") {

  # 判断是单组数据还是双组数据
  if (is.null(group2_data)) {
    # 单组数据：显示三次测量的个别结果
    plot_data <- data.frame(
      group = factor(paste("Test", 1:length(group1_data))),
      value = group1_data,
      x_pos = 1:length(group1_data)
    )

    # 计算均值和标准差用于标题显示
    mean_val <- mean(group1_data)
    sd_val <- sd(group1_data)

  } else {
    # 双组数据：显示两组的均值比较
    means <- c(mean(group1_data), mean(group2_data))
    errors <- c(sd(group1_data), sd(group2_data))

    plot_data <- data.frame(
      group = factor(c(group1_label, group2_label),
                    levels = c(group1_label, group2_label)),
      mean_value = means,
      error = errors,
      x_pos = c(1, 2)
    )
  }

  # 根据数据类型创建不同的图形
  if (is.null(group2_data)) {
    # 单组数据：显示三次测量结果
    max_value <- max(group1_data, na.rm = TRUE)
    
    p <- ggplot(plot_data, aes(x = x_pos, y = value)) +

      # 添加柱状图
      geom_col(fill = rgb(0, 0.45, 0.74), width = 0.6, color = "black", linewidth = 0.5) +

      # 添加数值标签
      geom_text(aes(label = round(value, 1)), vjust = -0.5, size = 4, fontface = "bold") +

      # 设置坐标轴
      scale_x_continuous(breaks = 1:length(group1_data),
                        labels = paste("Test", 1:length(group1_data)),
                        expand = expansion(mult = c(0.1, 0.1))) +
      scale_y_continuous(limits = c(0, max_value * 1.2), 
                        expand = expansion(mult = c(0, 0.05))) +

      # 设置标签
      labs(x = "Test Number",
           y = "Value")

  } else {
    # 双组数据：显示两组均值比较
    max_value <- max(c(group1_data, group2_data), na.rm = TRUE)
    
    p <- ggplot(plot_data, aes(x = x_pos, y = mean_value)) +

      # 添加柱状图，对应MATLAB的颜色
      geom_col(aes(fill = group), width = 0.4, color = "black", linewidth = 0.5) +

      # 添加误差棒
      geom_errorbar(aes(ymin = mean_value - error, ymax = mean_value + error),
                    width = 0.1, linewidth = 1.5, color = "black") +

      # 设置颜色，通用颜色方案
      scale_fill_manual(values = c(rgb(0, 0.45, 0.74),   # 蓝色
                                  rgb(0.85, 0.33, 0.1))) + # 红色

      # 设置坐标轴
      scale_x_continuous(breaks = c(1, 2),
                        labels = c(group1_label, group2_label),
                        expand = expansion(mult = c(0.1, 0.1))) +
      scale_y_continuous(limits = c(0, max_value * 1.1), 
                        expand = expansion(mult = c(0, 0.05))) +

      # 设置标签
      labs(x = "Group",
           y = "Value")
  }

  # 应用通用主题样式，按照规则要求
  p <- p +
    theme_bw() +
    theme(
      # 背景设置 - 统一白色背景
      plot.background = element_rect(fill = "white", color = NA),
      panel.background = element_rect(fill = "white", color = NA),
      
      # 全局字体设置 - 统一Times New Roman
      text = element_text(family = "times", color = "black"),
      
      # 坐标轴文字设置 - 固定字号和颜色
      axis.text.x = element_text(angle = 0, hjust = 0.5, size = 11, color = "black"),
      axis.text.y = element_text(size = 10, color = "black"),
      
      # 坐标轴标题设置 - 粗体，固定字号
      axis.title.x = element_text(size = 12, face = "bold", color = "black"),
      axis.title.y =  element_text(size = 12, face = "bold", color = "black"),
      
      # 网格线和边框设置 - 完整边框，无网格线
      panel.grid = element_blank(),  # 必须移除所有网格线
      axis.line = element_blank(),   # 移除轴线，避免与边框重复
      panel.border = element_rect(color = "black", fill = NA, linewidth = 0.6),
      
      # 刻度线设置 - 朝内，统一粗细
      axis.ticks = element_line(color = "black", linewidth = 0.6),
      axis.ticks.length = unit(-0.2, "cm"),  # 负值表示朝内
      
      # 边距设置 - 标准边距
      plot.margin = margin(10, 10, 0, 10),  # 上右下左边距
      
      # 图例设置
      legend.position = "none"
    )

  return(p)
}

# 为了向后兼容性，保留原函数名作为别名
# 在粘附力测试项目中使用时，可以调用此函数
plotPeelingEnergy <- function(group1_data = c(20.3319, 25.7963, 21.8242),
                             group2_data = NULL,
                             group1_label = "2733+Silbione",
                             group2_label = "2733") {
  # 调用通用的柱状图函数，但使用特定的标签
  p <- plotBarChart(group1_data, group2_data, group1_label, group2_label)

  # 为粘附力测试添加特定的Y轴标签
  # p <- p + labs(y = expression(paste("Peeling Energy (J/m"^"2", ")")))
  p <- p + labs(y = expression(bold(paste("Peeling Energy (J/m"^"2", ")"))))

  # 如果是双组数据，使用特定的X轴标签
  if (!is.null(group2_data)) {
    p <- p + labs(x = "Adhesive substrate")
  }

  return(p)
}

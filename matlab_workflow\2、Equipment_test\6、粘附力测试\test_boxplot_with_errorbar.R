# 测试带误差带的箱线图（使用stat_boxplot方法）
# 加载函数
source("5. Signal plotting/R/plotBoxPlot.R")

# 测试数据
group1_data <- c(20.3319, 25.7963, 21.8242, 22.5, 24.1, 19.8, 23.2)
group2_data <- c(18.5, 19.2, 20.1, 17.8, 19.9, 18.9, 20.5)

print("=== 测试数据 ===")
print(paste("Group 1:", paste(group1_data, collapse = ", ")))
print(paste("Group 2:", paste(group2_data, collapse = ", ")))

# 创建单组数据的箱线图（带误差带）
print("=== 创建单组箱线图 ===")
p1 <- plotBoxPlot(group1_data = group1_data,
                  group1_label = "Test Group",
                  show_points = TRUE,
                  show_mean = TRUE)

print("单组数据箱线图已创建，包含误差带（须线）")

# 创建双组数据的箱线图（带误差带）
print("=== 创建双组箱线图 ===")
p2 <- plotBoxPlot(group1_data = group1_data,
                  group2_data = group2_data,
                  group1_label = "Group A",
                  group2_label = "Group B",
                  show_points = TRUE,
                  show_mean = TRUE)

print("双组数据箱线图已创建，包含误差带（须线）")

# 保存图片
print("=== 保存图片 ===")
ggsave("single_group_boxplot_with_errorbar.png", p1, width = 6, height = 4, dpi = 300)
ggsave("dual_group_boxplot_with_errorbar.png", p2, width = 8, height = 4, dpi = 300)

print("图片已保存")

# 显示图形（如果在交互环境中）
print(p1)
print(p2)

% 清除工作区和窗口
clear all
clc
close all

% 定义两组数据
group1 = [20.3319, 25.7963, 21.8242];
group2 = [74.4408, 86.7685, 76.9097];


% 计算每组数据的均值
means = [mean(group1), mean(group2)];

% 计算每组数据的标准差（作为误差）
errors = [std(group1), std(group2)];

% 创建柱状图
figure;
set(gcf, 'Position', [300, 300, 800, 600]);  % 设置图像的位置和大小
hold on;
bar(1, means(1), 'FaceColor', [0 0.45 0.74], 'BarWidth', 0.4);  % 蓝色柱 (group 1)
bar(2, means(2), 'FaceColor', [0.85 0.33 0.1], 'BarWidth', 0.4);  % 红色柱 (group 2)

% 添加误差棒
errorbar(1, means(1), errors(1), 'k', 'LineWidth', 1.5);  % 第一个柱的误差棒
errorbar(2, means(2), errors(2), 'k', 'LineWidth', 1.5);  % 第二个柱的误差棒

% 设置图形属性
ax = gca;
ax.FontSize = 18; % 设置刻度尺文字大小
ax.FontName = 'Times New Roman'; % 设置刻度尺字体
ax.FontWeight = "bold";
ax.YLim = [0 90];
xlabel('Adhesive substrate', 'FontSize', 20, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
ylabel('Peeling Energy (J/m^2)', 'FontSize', 20, 'FontName', 'Times New Roman', 'FontWeight', 'bold');


% 打开上、右边的边框
box on;


% 设置X轴刻度标签
xticks([1 2]);
xticklabels({'2733+Silbione', '2733'});  % X轴标签为 Group 1 和 Group 2


hold off;

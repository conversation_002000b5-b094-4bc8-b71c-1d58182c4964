# matlab-dev-agent-checklist

Dev-agent inspired MATLAB quality checklist with sequential task execution and quality gate discipline.

## Purpose
Validate MATLAB code following dev-agent principles: sequential execution, quality gates, and comprehensive validation.

## Dev-Agent Compliance Checks

### Task Execution Flow Validation
- [ ] Read task→Implement MATLAB solution→Write tests→Run performance check→Validate results→Update [x]→Next task
- [ ] All tasks marked with proper status: [ ] not started | [-] in progress | [x] complete
- [ ] Sequential task execution maintained (no skipping ahead)
- [ ] Each task completed before moving to next

### Quality Gate Discipline
- [ ] NEVER complete tasks with failing automated validations
- [ ] Syntax check passes (MATLAB Code Analyzer clean)
- [ ] Performance check meets configured benchmarks
- [ ] Test coverage threshold achieved (>80%)
- [ ] Documentation check completed
- [ ] Vectorization check performed (>80% ratio where applicable)

### MATLAB-Centric Principles
- [ ] Focus only on MATLAB-specific solutions and best practices
- [ ] Performance-first approach with vectorization consideration
- [ ] Toolbox dependencies checked and alternatives provided
- [ ] MATLAB coding standards followed per loaded configuration

### Record Keeping (Dev-Agent Style)
- [ ] Performance Log updated: | Task | Execution Time | Memory Usage | Optimization Applied |
- [ ] MATLAB Files tracked: All .m, .mat, .fig, .mlx files listed
- [ ] Toolbox Dependencies documented with versions
- [ ] Test Results recorded with benchmarks
- [ ] Completion Notes: Deviations from requirements (<50 words)
- [ ] Change Log: Requirement changes only

### Debug Log Discipline
- [ ] Temporary changes logged to matlabDebugLog table
- [ ] Debug entries include: | Task | File | Change | Reverted? |
- [ ] All temporary changes reverted after fix
- [ ] Debug log maintained throughout development

### Blocking Conditions (HALT for)
- [ ] Missing required MATLAB toolbox
- [ ] Ambiguous requirements after clarification attempts
- [ ] 3 consecutive failures in validation
- [ ] Missing MATLAB configuration files
- [ ] Failing performance validations

## MATLAB-Specific Validations

### Code Quality
- [ ] MATLAB syntax validation passes (mlint clean)
- [ ] Vectorization analysis completed (>80% where applicable)
- [ ] Memory usage optimized (arrays preallocated)
- [ ] Built-in MATLAB functions used instead of custom implementations
- [ ] Error handling implemented for edge cases

### Performance Benchmarks
- [ ] Execution time meets configured limits (default: <1000ms)
- [ ] Memory usage within limits (default: <100MB)
- [ ] Vectorization ratio acceptable (default: >80%)
- [ ] Profiling completed and bottlenecks addressed

### Testing Framework
- [ ] Unit tests written using matlab.unittest framework
- [ ] All tests pass without failures
- [ ] Test coverage >80% of code lines
- [ ] Performance tests included where applicable
- [ ] Edge cases and error conditions tested

### Documentation Standards
- [ ] Function headers include purpose, inputs, outputs
- [ ] Help text accessible via help command
- [ ] Examples provided for complex functions
- [ ] Algorithm complexity documented
- [ ] Toolbox dependencies clearly stated

### File Management
- [ ] All created/modified files tracked in File List
- [ ] MATLAB file types properly handled (.m, .mat, .fig, .mlx, .slx)
- [ ] Version control ready (clean history)
- [ ] No temporary or debug files left behind

## Completion Criteria (Dev-Agent Style)

### Task Ready for Review
- [ ] All [x]→Syntax valid→Tests pass→Performance acceptable→Toolbox check→Update File List→Mark Ready for Review→HALT
- [ ] Code matches requirements exactly
- [ ] All validations pass
- [ ] Follows MATLAB standards per loaded configuration
- [ ] Performance benchmarks met
- [ ] File List complete and accurate

### Quality Assurance
- [ ] No blocking conditions present
- [ ] Debug log discipline maintained
- [ ] Sequential task execution completed
- [ ] Quality gate discipline followed throughout
- [ ] MATLAB-centric principles applied

## Final Assessment

**Dev-Agent Compliance Score:** ___/10

**Task Execution Status:**
- [ ] ✅ All tasks completed sequentially
- [ ] ✅ Quality gates passed at each step
- [ ] ✅ Debug log discipline maintained
- [ ] ✅ MATLAB-centric approach followed
- [ ] ❌ Issues identified - see below

**Blocking Issues:**
1. 
2. 
3. 

**Performance Metrics:**
- Execution Time: ___ms (limit: 1000ms)
- Memory Usage: ___MB (limit: 100MB)
- Vectorization Ratio: ___%  (target: >80%)
- Test Coverage: ___% (target: >80%)

**File Tracking:**
- .m files: ___
- .mat files: ___
- .fig files: ___
- .mlx files: ___
- Total files: ___

**Ready for Next Phase:**
- [ ] ✅ READY FOR REVIEW
- [ ] ✅ READY FOR INTEGRATION
- [ ] ✅ READY FOR DEPLOYMENT
- [ ] ❌ NOT READY - Address blocking issues

**Reviewer:** _______________  
**Date:** _______________  
**Dev-Agent Session:** _______________

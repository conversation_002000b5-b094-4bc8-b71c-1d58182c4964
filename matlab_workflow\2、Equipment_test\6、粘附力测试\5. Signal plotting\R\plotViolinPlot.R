# ==========================================
# 文件名：plotViolinPlot.R
# 主要功能：绘制通用小提琴图（可移植到其他项目）
# 依赖文件：无
# 主要函数：
#   - plotViolinPlot(): 绘制单组或双组数据对比小提琴图
#   - plotPeelingEnergyViolinPlot(): 专门用于粘附力测试的小提琴图
# 输入参数：一组或两组数值数据，组标签
# 输出结果：返回ggplot对象供用户手动保存
# 创建日期：2025-08-01
# ==========================================

# 加载必要的包
library(ggplot2)
library(ggsci)  # 用于专业配色方案

# Windows系统字体设置
if (.Platform$OS.type == "windows") {
  windowsFonts(times = windowsFont("Times New Roman"))
}

#' 绘制通用小提琴图
#' 可移植到其他项目使用的通用小提琴图绘制函数
#' 小提琴图结合了箱线图和核密度估计，能够显示数据的分布形状
#'
#' @param group1_data 第一组数值数据
#' @param group2_data 第二组数值数据（可选，为NULL时只显示单组数据）
#' @param group1_label 第一组标签
#' @param group2_label 第二组标签（可选）
#' @param show_points 是否显示原始数据点（默认TRUE）
#' @param show_mean 是否显示均值点（默认TRUE）
#' @param show_median 是否显示中位数线（默认TRUE）
#' @param show_quartiles 是否显示四分位数线（默认FALSE）
#' @param show_boxplot 是否显示箱线图（默认FALSE）
#' @param show_errorbar 是否显示误差线（默认FALSE）
#' @param error_type 误差线类型："sd"(标准差), "se"(标准误), "ci"(置信区间)（默认"sd"）
#' @param violin_width 小提琴图的宽度（默认0.8）
#' @param trim_tails 是否修剪小提琴图的尾部（默认TRUE）
#' @param color_scheme 配色方案："default"(默认), "jco", "nejm", "lancet", "aaas"（默认"default"）
#' @return ggplot对象
plotViolinPlot <- function(group1_data = c(20.3319, 25.7963, 21.8242),
                          group2_data = NULL,
                          group1_label = "Group 1",
                          group2_label = "Group 2",
                          show_points = TRUE,
                          show_mean = TRUE,
                          show_median = TRUE,
                          show_quartiles = FALSE,
                          show_boxplot = FALSE,
                          show_errorbar = FALSE,
                          error_type = "sd",
                          violin_width = 0.8,
                          trim_tails = TRUE,
                          color_scheme = "default") {

  # 验证参数
  if (!error_type %in% c("sd", "se", "ci")) {
    stop("error_type must be one of: 'sd', 'se', 'ci'")
  }
  if (!color_scheme %in% c("default", "jco", "nejm", "lancet", "aaas")) {
    stop("color_scheme must be one of: 'default', 'jco', 'nejm', 'lancet', 'aaas'")
  }

  # 判断是单组数据还是双组数据
  if (is.null(group2_data)) {
    # 单组数据：显示单个组的小提琴图
    plot_data <- data.frame(
      group = factor(rep(group1_label, length(group1_data))),
      value = group1_data
    )
  } else {
    # 双组数据：显示两组的小提琴图比较
    plot_data <- data.frame(
      group = factor(c(rep(group1_label, length(group1_data)),
                      rep(group2_label, length(group2_data))),
                    levels = c(group1_label, group2_label)),
      value = c(group1_data, group2_data)
    )
  }

  # 创建基础小提琴图
  p <- ggplot(plot_data, aes(x = group, y = value))

  # 添加小提琴图
  if (is.null(group2_data)) {
    # 单组数据：使用单一颜色
    if (color_scheme == "default") {
      p <- p + geom_violin(fill = rgb(0, 0.45, 0.74),
                          color = "black",
                          linewidth = 0.8,
                          width = violin_width,
                          alpha = 0.7,
                          trim = trim_tails,
                          scale = "width")
    } else {
      # 使用ggsci配色方案
      p <- p + geom_violin(fill = rgb(0, 0.45, 0.74),
                          color = "black",
                          linewidth = 0.8,
                          width = violin_width,
                          alpha = 0.7,
                          trim = trim_tails,
                          scale = "width")
    }
  } else {
    # 双组数据：使用不同颜色
    p <- p + geom_violin(aes(fill = group),
                        color = "black",
                        linewidth = 0.8,
                        width = violin_width,
                        alpha = 0.7,
                        trim = trim_tails,
                        scale = "width")

    # 根据配色方案设置颜色
    if (color_scheme == "default") {
      p <- p + scale_fill_manual(values = c(rgb(0, 0.45, 0.74),
                                           rgb(0.85, 0.33, 0.1)))
    } else if (color_scheme == "jco") {
      p <- p + scale_fill_jco()
    } else if (color_scheme == "nejm") {
      p <- p + scale_fill_nejm()
    } else if (color_scheme == "lancet") {
      p <- p + scale_fill_lancet()
    } else if (color_scheme == "aaas") {
      p <- p + scale_fill_aaas()
    }
  }
  
  # 添加箱线图（如果需要）
  if (show_boxplot) {
    p <- p + geom_boxplot(linewidth = 0.4, width = 0.3,
                         fill = "white", alpha = 0.8,
                         outlier.shape = NA)  # 避免异常值重复显示
  }

  # 添加中位数线（如果需要且没有显示箱线图）
  if (show_median && !show_boxplot) {
    p <- p + stat_summary(fun = median, geom = "crossbar",
                         width = 0.4, color = "white",
                         linewidth = 2, fatten = 0)
  }
  
  # 添加四分位数线（如果需要且没有显示箱线图）
  if (show_quartiles && !show_boxplot) {
    # 添加第一四分位数线
    p <- p + stat_summary(fun = function(x) quantile(x, 0.25),
                         geom = "crossbar", width = 0.3,
                         color = "white", linewidth = 1,
                         linetype = "dashed", fatten = 0)
    # 添加第三四分位数线
    p <- p + stat_summary(fun = function(x) quantile(x, 0.75),
                         geom = "crossbar", width = 0.3,
                         color = "white", linewidth = 1,
                         linetype = "dashed", fatten = 0)
  }
  
  # 添加原始数据点（如果需要）
  if (show_points) {
    if (is.null(group2_data)) {
      # 单组数据：添加抖动点
      p <- p + geom_jitter(width = 0.1,
                          size = 2.5,
                          color = "black",
                          fill = "white",
                          shape = 21,
                          stroke = 1,
                          alpha = 0.8)
    } else {
      # 双组数据：添加抖动点
      p <- p + geom_jitter(aes(fill = group),
                          width = 0.1,
                          size = 2.5,
                          color = "black",
                          shape = 21,
                          stroke = 1,
                          alpha = 0.8)
    }
  }

  # 添加误差线（如果需要）
  if (show_errorbar) {
    if (error_type == "sd") {
      # 均值 ± 标准差
      p <- p + stat_summary(fun.data = function(x) {
        data.frame(y = mean(x),
                  ymin = mean(x) - sd(x),
                  ymax = mean(x) + sd(x))
      }, geom = "pointrange", size = 1, color = "black")
    } else if (error_type == "se") {
      # 均值 ± 标准误
      p <- p + stat_summary(fun.data = function(x) {
        se <- sd(x) / sqrt(length(x))
        data.frame(y = mean(x),
                  ymin = mean(x) - se,
                  ymax = mean(x) + se)
      }, geom = "pointrange", size = 1, color = "black")
    } else if (error_type == "ci") {
      # 均值 ± 95%置信区间
      p <- p + stat_summary(fun.data = function(x) {
        ci <- qt(0.975, length(x) - 1) * sd(x) / sqrt(length(x))
        data.frame(y = mean(x),
                  ymin = mean(x) - ci,
                  ymax = mean(x) + ci)
      }, geom = "pointrange", size = 1, color = "black")
    }
  }

  # 添加均值点（如果需要且没有显示误差线）
  if (show_mean && !show_errorbar) {
    p <- p + stat_summary(fun = mean, geom = "point",
                         size = 4, color = "red", shape = 18)
  }

  # 设置坐标轴
  if (is.null(group2_data)) {
    # 单组数据：设置单个组的坐标轴
    max_value <- max(group1_data, na.rm = TRUE)
    p <- p +
      scale_x_discrete(expand = expansion(mult = c(0.4, 0.4))) +  # 增加左右间距
      scale_y_continuous(limits = c(0, max_value * 1.2),
                        expand = expansion(mult = c(0, 0.05))) +
      # 设置标签
      labs(x = "Test Group",
           y = "Value")
  } else {
    # 双组数据：设置两组比较的坐标轴
    max_value <- max(c(group1_data, group2_data), na.rm = TRUE)
    p <- p +
      scale_x_discrete(expand = expansion(mult = c(0.4, 0.4))) +  # 增加左右间距
      scale_y_continuous(limits = c(0, max_value * 1.2),
                        expand = expansion(mult = c(0, 0.05))) +
      # 设置标签
      labs(x = "Group",
           y = "Value")
  }

  # 应用通用主题样式，与其他图表保持一致
  p <- p +
    theme_bw() +
    theme(
      # 背景设置 - 统一白色背景
      plot.background = element_rect(fill = "white", color = NA),
      panel.background = element_rect(fill = "white", color = NA),
      
      # 全局字体设置 - 统一Times New Roman
      text = element_text(family = "times", color = "black"),
      
      # 坐标轴文字设置 - 固定字号和颜色
      axis.text.x = element_text(angle = 0, hjust = 0.5, size = 11, color = "black"),
      axis.text.y = element_text(size = 10, color = "black"),
      
      # 坐标轴标题设置 - 粗体，固定字号
      axis.title.x = element_text(size = 12, face = "bold", color = "black"),
      axis.title.y = element_text(size = 12, face = "bold", color = "black"),
      
      # 网格线和边框设置 - 完整边框，无网格线
      panel.grid = element_blank(),  # 必须移除所有网格线
      axis.line = element_blank(),   # 移除轴线，避免与边框重复
      panel.border = element_rect(color = "black", fill = NA, linewidth = 0.6),
      
      # 刻度线设置 - 朝内，统一粗细
      axis.ticks = element_line(color = "black", linewidth = 0.6),
      axis.ticks.length = unit(-0.2, "cm"),  # 负值表示朝内
      
      # 边距设置 - 标准边距
      plot.margin = margin(10, 10, 0, 10),  # 上右下左边距
      
      # 图例设置
      legend.position = "none"
    )

  return(p)
}

#' 为粘附力测试专门设计的小提琴图函数
#' 在粘附力测试项目中使用时，可以调用此函数
#'
#' @param group1_data 第一组剥离能量数据
#' @param group2_data 第二组剥离能量数据（可选）
#' @param group1_label 第一组标签（默认为"2733+Silbione"）
#' @param group2_label 第二组标签（默认为"2733"）
#' @param show_points 是否显示原始数据点（默认TRUE）
#' @param show_mean 是否显示均值点（默认TRUE）
#' @param show_median 是否显示中位数线（默认TRUE）
#' @param show_quartiles 是否显示四分位数线（默认FALSE）
#' @param show_boxplot 是否显示箱线图（默认FALSE）
#' @param show_errorbar 是否显示误差线（默认FALSE）
#' @param error_type 误差线类型："sd", "se", "ci"（默认"sd"）
#' @param violin_width 小提琴图的宽度（默认0.8）
#' @param trim_tails 是否修剪小提琴图的尾部（默认TRUE）
#' @param color_scheme 配色方案（默认"default"）
#' @return ggplot对象
plotPeelingEnergyViolinPlot <- function(group1_data = c(20.3319, 25.7963, 21.8242),
                                       group2_data = NULL,
                                       group1_label = "2733+Silbione",
                                       group2_label = "2733",
                                       show_points = TRUE,
                                       show_mean = TRUE,
                                       show_median = TRUE,
                                       show_quartiles = FALSE,
                                       show_boxplot = FALSE,
                                       show_errorbar = FALSE,
                                       error_type = "sd",
                                       violin_width = 0.8,
                                       trim_tails = TRUE,
                                       color_scheme = "default") {

  # 调用通用的小提琴图函数
  p <- plotViolinPlot(group1_data, group2_data, group1_label, group2_label,
                     show_points, show_mean, show_median, show_quartiles,
                     show_boxplot, show_errorbar, error_type,
                     violin_width, trim_tails, color_scheme)

  # 为粘附力测试添加特定的Y轴标签
  p <- p + labs(y = expression(bold(paste("Peeling Energy (J/m"^"2", ")"))))

  # 如果是双组数据，使用特定的X轴标签
  if (!is.null(group2_data)) {
    p <- p + labs(x = "Adhesive substrate")
  } else {
    p <- p + labs(x = "Test Group")
  }

  return(p)
}
